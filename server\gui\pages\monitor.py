"""
实时监控页面
"""
import customtkinter as ctk
from tkinter import ttk
import threading
import time
import psutil
from datetime import datetime
from server.config.config import GUIConfig

class MonitorPage:
    """实时监控页面类"""
    
    def __init__(self, parent, main_window):
        self.parent = parent
        self.main_window = main_window
        self.is_visible = False
        self.monitoring = False
        
        self.create_widgets()
        
    def create_widgets(self):
        """创建页面组件"""
        # 创建主框架
        self.frame = ctk.CTkScrollableFrame(self.parent)
        
        # 页面标题
        self.title_label = ctk.CTkLabel(
            self.frame,
            text="📈 实时监控",
            font=self.main_window.title_font,
            anchor="w"
        )
        self.title_label.pack(fill="x", padx=20, pady=(20, 10))
        
        # 创建监控面板
        self.create_system_monitor()
        self.create_user_monitor()
        self.create_file_monitor()
        self.create_network_monitor()
        
    def create_system_monitor(self):
        """创建系统监控面板"""
        # 系统监控框架
        self.system_frame = ctk.CTkFrame(self.frame)
        self.system_frame.pack(fill="x", padx=20, pady=10)
        
        system_title = ctk.CTkLabel(
            self.system_frame,
            text="🖥️ 系统资源监控",
            font=self.main_window.title_font
        )
        system_title.pack(pady=10)
        
        # 系统信息网格
        self.system_info_frame = ctk.CTkFrame(self.system_frame)
        self.system_info_frame.pack(fill="x", padx=10, pady=10)
        
        # 配置网格
        for i in range(4):
            self.system_info_frame.grid_columnconfigure(i, weight=1)
            
        # CPU监控
        self.cpu_frame = ctk.CTkFrame(self.system_info_frame)
        self.cpu_frame.grid(row=0, column=0, padx=5, pady=5, sticky="ew")
        
        cpu_label = ctk.CTkLabel(self.cpu_frame, text="CPU使用率", font=self.main_window.title_font)
        cpu_label.pack(pady=5)
        
        self.cpu_progress = ctk.CTkProgressBar(self.cpu_frame)
        self.cpu_progress.pack(pady=5, padx=10, fill="x")
        
        self.cpu_text = ctk.CTkLabel(self.cpu_frame, text="0%")
        self.cpu_text.pack(pady=5)
        
        # 内存监控
        self.memory_frame = ctk.CTkFrame(self.system_info_frame)
        self.memory_frame.grid(row=0, column=1, padx=5, pady=5, sticky="ew")
        
        memory_label = ctk.CTkLabel(self.memory_frame, text="内存使用率", font=self.main_window.title_font)
        memory_label.pack(pady=5)
        
        self.memory_progress = ctk.CTkProgressBar(self.memory_frame)
        self.memory_progress.pack(pady=5, padx=10, fill="x")
        
        self.memory_text = ctk.CTkLabel(self.memory_frame, text="0%")
        self.memory_text.pack(pady=5)
        
        # 磁盘监控
        self.disk_frame = ctk.CTkFrame(self.system_info_frame)
        self.disk_frame.grid(row=0, column=2, padx=5, pady=5, sticky="ew")
        
        disk_label = ctk.CTkLabel(self.disk_frame, text="磁盘使用率", font=self.main_window.title_font)
        disk_label.pack(pady=5)
        
        self.disk_progress = ctk.CTkProgressBar(self.disk_frame)
        self.disk_progress.pack(pady=5, padx=10, fill="x")
        
        self.disk_text = ctk.CTkLabel(self.disk_frame, text="0%")
        self.disk_text.pack(pady=5)
        
        # 网络监控
        self.network_frame = ctk.CTkFrame(self.system_info_frame)
        self.network_frame.grid(row=0, column=3, padx=5, pady=5, sticky="ew")
        
        network_label = ctk.CTkLabel(self.network_frame, text="网络状态", font=self.main_window.title_font)
        network_label.pack(pady=5)
        
        self.network_status = ctk.CTkLabel(self.network_frame, text="🟢 正常")
        self.network_status.pack(pady=5)
        
        self.network_speed = ctk.CTkLabel(self.network_frame, text="0 KB/s")
        self.network_speed.pack(pady=5)
        
    def create_user_monitor(self):
        """创建用户监控面板"""
        # 用户监控框架
        self.user_frame = ctk.CTkFrame(self.frame)
        self.user_frame.pack(fill="x", padx=20, pady=10)
        
        user_title = ctk.CTkLabel(
            self.user_frame,
            text="👥 用户活动监控",
            font=self.main_window.title_font
        )
        user_title.pack(pady=10)
        
        # 在线用户列表
        self.online_users_frame = ctk.CTkFrame(self.user_frame)
        self.online_users_frame.pack(fill="x", padx=10, pady=10)
        
        online_title = ctk.CTkLabel(self.online_users_frame, text="在线用户")
        online_title.pack(pady=5)
        
        # 在线用户表格
        columns = ("用户名", "IP地址", "登录时间", "最后活动", "状态")
        self.online_tree = ttk.Treeview(self.online_users_frame, columns=columns, show="headings", height=6)
        
        for col in columns:
            self.online_tree.heading(col, text=col)
            self.online_tree.column(col, width=120)
            
        self.online_tree.pack(fill="x", padx=10, pady=10)
        
        # 加载在线用户数据
        self.load_online_users()
        
    def create_file_monitor(self):
        """创建文件监控面板"""
        # 文件监控框架
        self.file_frame = ctk.CTkFrame(self.frame)
        self.file_frame.pack(fill="x", padx=20, pady=10)
        
        file_title = ctk.CTkLabel(
            self.file_frame,
            text="📁 文件操作监控",
            font=self.main_window.title_font
        )
        file_title.pack(pady=10)
        
        # 文件操作统计
        self.file_stats_frame = ctk.CTkFrame(self.file_frame)
        self.file_stats_frame.pack(fill="x", padx=10, pady=10)
        
        # 配置网格
        for i in range(4):
            self.file_stats_frame.grid_columnconfigure(i, weight=1)
            
        # 今日下载
        self.download_stat = self.create_stat_widget(
            self.file_stats_frame, "⬇️", "今日下载", "0", 0, 0
        )
        
        # 今日上传
        self.upload_stat = self.create_stat_widget(
            self.file_stats_frame, "⬆️", "今日上传", "0", 0, 1
        )
        
        # 今日搜索
        self.search_stat = self.create_stat_widget(
            self.file_stats_frame, "🔍", "今日搜索", "0", 0, 2
        )
        
        # 活跃文件
        self.active_stat = self.create_stat_widget(
            self.file_stats_frame, "🔥", "热门文件", "0", 0, 3
        )
        
    def create_network_monitor(self):
        """创建网络监控面板"""
        # 网络监控框架
        self.network_monitor_frame = ctk.CTkFrame(self.frame)
        self.network_monitor_frame.pack(fill="x", padx=20, pady=10)
        
        network_title = ctk.CTkLabel(
            self.network_monitor_frame,
            text="🌐 网络流量监控",
            font=self.main_window.title_font
        )
        network_title.pack(pady=10)
        
        # 网络流量图表（简化版）
        self.traffic_frame = ctk.CTkFrame(self.network_monitor_frame)
        self.traffic_frame.pack(fill="x", padx=10, pady=10)
        
        self.traffic_text = ctk.CTkTextbox(
            self.traffic_frame,
            height=100,
            font=ctk.CTkFont(family="Consolas", size=10)
        )
        self.traffic_text.pack(fill="x", padx=10, pady=10)
        
        # 更新网络流量显示
        self.update_traffic_display()
        
    def create_stat_widget(self, parent, icon, title, value, row, col):
        """创建统计小部件"""
        widget_frame = ctk.CTkFrame(parent)
        widget_frame.grid(row=row, column=col, padx=5, pady=5, sticky="ew")
        
        icon_label = ctk.CTkLabel(widget_frame, text=icon, font=ctk.CTkFont(size=20))
        icon_label.pack(pady=5)
        
        title_label = ctk.CTkLabel(widget_frame, text=title)
        title_label.pack()
        
        value_label = ctk.CTkLabel(
            widget_frame,
            text=value,
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=GUIConfig.COLORS["primary"]
        )
        value_label.pack(pady=5)
        
        return value_label
        
    def load_online_users(self):
        """加载在线用户"""
        # 清空现有数据
        for item in self.online_tree.get_children():
            self.online_tree.delete(item)
            
        # 模拟在线用户数据
        users = [
            ("admin", "192.168.1.100", "10:30:15", "10:35:22", "活跃"),
            ("user1", "192.168.1.101", "09:15:30", "10:34:45", "活跃"),
            ("user2", "192.168.1.102", "08:45:12", "10:20:18", "空闲"),
        ]
        
        for user in users:
            self.online_tree.insert("", "end", values=user)
            
    def update_system_stats(self):
        """更新系统统计"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            self.cpu_progress.set(cpu_percent / 100)
            self.cpu_text.configure(text=f"{cpu_percent:.1f}%")
            
            # 内存使用率
            memory = psutil.virtual_memory()
            self.memory_progress.set(memory.percent / 100)
            self.memory_text.configure(text=f"{memory.percent:.1f}%")
            
            # 磁盘使用率
            disk = psutil.disk_usage('/')
            self.disk_progress.set(disk.percent / 100)
            self.disk_text.configure(text=f"{disk.percent:.1f}%")
            
            # 网络状态
            try:
                import socket
                socket.create_connection(("8.8.8.8", 53), timeout=3)
                self.network_status.configure(text="🟢 正常")
            except:
                self.network_status.configure(text="🔴 异常")
                
        except Exception as e:
            print(f"更新系统统计错误: {e}")
            
    def update_traffic_display(self):
        """更新网络流量显示"""
        traffic_data = """
网络流量监控 (最近1小时)

时间        上行(KB/s)    下行(KB/s)
10:30       125.3         456.7
10:25       98.2          234.5
10:20       156.8         678.9
10:15       87.4          123.4
10:10       234.6         567.8
10:05       145.2         345.6
10:00       98.7          234.5

总计: 上行 1.2MB | 下行 3.4MB
        """
        
        self.traffic_text.delete("1.0", "end")
        self.traffic_text.insert("1.0", traffic_data)
        
    def start_monitoring(self):
        """开始监控"""
        if not self.monitoring:
            self.monitoring = True
            monitor_thread = threading.Thread(target=self.monitor_loop, daemon=True)
            monitor_thread.start()
            
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        
    def monitor_loop(self):
        """监控循环"""
        while self.monitoring and self.is_visible:
            self.update_system_stats()
            time.sleep(5)  # 每5秒更新一次
            
    def show(self):
        """显示页面"""
        self.frame.pack(fill="both", expand=True)
        self.is_visible = True
        self.start_monitoring()
        
    def hide(self):
        """隐藏页面"""
        self.frame.pack_forget()
        self.is_visible = False
        self.stop_monitoring()
