#!/usr/bin/env python3
"""
服务端启动脚本
"""
import sys
import os
import argparse
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def run_gui():
    """运行GUI管理界面"""
    try:
        from server.gui.main_window import main
        main()
    except ImportError as e:
        print(f"导入GUI模块失败: {e}")
        print("请确保已安装所需依赖: pip install -r requirements.txt")
        sys.exit(1)
    except Exception as e:
        print(f"启动GUI失败: {e}")
        sys.exit(1)

def run_api():
    """运行API服务"""
    try:
        from server.api.app import create_app
        app = create_app('development')
        app.run(host='0.0.0.0', port=5000, debug=True)
    except ImportError as e:
        print(f"导入API模块失败: {e}")
        print("请确保已安装所需依赖: pip install -r requirements.txt")
        sys.exit(1)
    except Exception as e:
        print(f"启动API服务失败: {e}")
        sys.exit(1)

def init_database():
    """初始化数据库"""
    try:
        import pymysql
        from server.config.config import Config
        
        # 连接MySQL服务器
        connection = pymysql.connect(
            host=Config.DB_HOST,
            port=Config.DB_PORT,
            user=Config.DB_USER,
            password=Config.DB_PASSWORD,
            charset=Config.DB_CHARSET
        )
        
        with connection.cursor() as cursor:
            # 创建数据库
            cursor.execute(f"CREATE DATABASE IF NOT EXISTS {Config.DB_NAME} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            print(f"数据库 {Config.DB_NAME} 创建成功")
        
        connection.close()
        
        # 执行SQL脚本
        sql_file = project_root / "database" / "schema.sql"
        if sql_file.exists():
            connection = pymysql.connect(
                host=Config.DB_HOST,
                port=Config.DB_PORT,
                user=Config.DB_USER,
                password=Config.DB_PASSWORD,
                database=Config.DB_NAME,
                charset=Config.DB_CHARSET
            )
            
            with open(sql_file, 'r', encoding='utf-8') as f:
                sql_content = f.read()
            
            # 分割SQL语句
            sql_statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
            
            with connection.cursor() as cursor:
                for statement in sql_statements:
                    if statement:
                        cursor.execute(statement)
            
            connection.commit()
            connection.close()
            print("数据库表结构创建成功")
        else:
            print(f"SQL文件不存在: {sql_file}")
            
    except ImportError:
        print("PyMySQL未安装，请运行: pip install pymysql")
        sys.exit(1)
    except Exception as e:
        print(f"初始化数据库失败: {e}")
        print("请检查MySQL服务是否运行，以及数据库连接配置是否正确")
        sys.exit(1)

def check_dependencies():
    """检查依赖"""
    required_packages = [
        'flask',
        'flask_sqlalchemy',
        'flask_jwt_extended',
        'flask_cors',
        'pymysql',
        'customtkinter',
        'pillow',
        'psutil'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("缺少以下依赖包:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\n请运行以下命令安装依赖:")
        print("pip install -r requirements.txt")
        return False
    
    return True

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='企业文件共享系统服务端')
    parser.add_argument(
        'mode',
        choices=['gui', 'api', 'init-db', 'check'],
        help='运行模式: gui(GUI管理界面), api(API服务), init-db(初始化数据库), check(检查依赖)'
    )
    
    args = parser.parse_args()
    
    print("=" * 50)
    print("企业文件共享系统 - 服务端")
    print("版本: 1.0.0")
    print("=" * 50)
    
    if args.mode == 'check':
        print("检查依赖...")
        if check_dependencies():
            print("✅ 所有依赖已安装")
        else:
            sys.exit(1)
    
    elif args.mode == 'init-db':
        print("初始化数据库...")
        if not check_dependencies():
            sys.exit(1)
        init_database()
        print("✅ 数据库初始化完成")
    
    elif args.mode == 'gui':
        print("启动GUI管理界面...")
        if not check_dependencies():
            sys.exit(1)
        run_gui()
    
    elif args.mode == 'api':
        print("启动API服务...")
        if not check_dependencies():
            sys.exit(1)
        run_api()

if __name__ == '__main__':
    main()
