"""
仪表板页面
"""
import customtkinter as ctk
from tkinter import ttk
import threading
import time
from datetime import datetime, timedelta
from server.config.config import GUIConfig

class DashboardPage:
    """仪表板页面类"""
    
    def __init__(self, parent, main_window):
        self.parent = parent
        self.main_window = main_window
        self.is_visible = False
        
        self.create_widgets()
        self.start_data_refresh()
        
    def create_widgets(self):
        """创建页面组件"""
        # 创建主框架
        self.frame = ctk.CTkScrollableFrame(self.parent)
        
        # 页面标题
        self.title_label = ctk.CTkLabel(
            self.frame,
            text="📊 系统仪表板",
            font=self.main_window.title_font,
            anchor="w"
        )
        self.title_label.pack(fill="x", padx=20, pady=(20, 10))
        
        # 创建统计卡片区域
        self.create_stats_cards()
        
        # 创建图表区域
        self.create_charts_area()
        
        # 创建活动日志区域
        self.create_activity_log()
        
        # 创建系统信息区域
        self.create_system_info()
        
    def create_stats_cards(self):
        """创建统计卡片"""
        # 统计卡片容器
        self.stats_frame = ctk.CTkFrame(self.frame)
        self.stats_frame.pack(fill="x", padx=20, pady=10)
        
        # 配置网格
        for i in range(4):
            self.stats_frame.grid_columnconfigure(i, weight=1)
            
        # 在线用户卡片
        self.online_users_card = self.create_stat_card(
            self.stats_frame, "👥", "在线用户", "0", "用户", 0, 0
        )
        
        # 文件总数卡片
        self.total_files_card = self.create_stat_card(
            self.stats_frame, "📁", "文件总数", "0", "个", 0, 1
        )
        
        # 今日下载卡片
        self.today_downloads_card = self.create_stat_card(
            self.stats_frame, "⬇️", "今日下载", "0", "次", 0, 2
        )
        
        # 存储使用卡片
        self.storage_usage_card = self.create_stat_card(
            self.stats_frame, "💾", "存储使用", "0", "GB", 0, 3
        )
        
    def create_stat_card(self, parent, icon, title, value, unit, row, col):
        """创建统计卡片"""
        card = ctk.CTkFrame(parent)
        card.grid(row=row, column=col, padx=10, pady=10, sticky="ew")
        
        # 图标
        icon_label = ctk.CTkLabel(
            card,
            text=icon,
            font=ctk.CTkFont(size=24)
        )
        icon_label.pack(pady=(15, 5))
        
        # 标题
        title_label = ctk.CTkLabel(
            card,
            text=title,
            font=self.main_window.default_font
        )
        title_label.pack()
        
        # 数值
        value_label = ctk.CTkLabel(
            card,
            text=value,
            font=ctk.CTkFont(size=20, weight="bold"),
            text_color=GUIConfig.COLORS["primary"]
        )
        value_label.pack()
        
        # 单位
        unit_label = ctk.CTkLabel(
            card,
            text=unit,
            font=ctk.CTkFont(size=10),
            text_color="gray"
        )
        unit_label.pack(pady=(0, 15))
        
        return {
            'card': card,
            'value_label': value_label,
            'unit_label': unit_label
        }
        
    def create_charts_area(self):
        """创建图表区域"""
        # 图表容器
        self.charts_frame = ctk.CTkFrame(self.frame)
        self.charts_frame.pack(fill="x", padx=20, pady=10)
        
        # 配置网格
        self.charts_frame.grid_columnconfigure(0, weight=1)
        self.charts_frame.grid_columnconfigure(1, weight=1)
        
        # 下载趋势图
        self.download_chart_frame = ctk.CTkFrame(self.charts_frame)
        self.download_chart_frame.grid(row=0, column=0, padx=10, pady=10, sticky="nsew")
        
        download_title = ctk.CTkLabel(
            self.download_chart_frame,
            text="📈 下载趋势（最近7天）",
            font=self.main_window.title_font
        )
        download_title.pack(pady=10)
        
        # 简单的文本图表（实际项目中可以使用matplotlib）
        self.download_chart_text = ctk.CTkTextbox(
            self.download_chart_frame,
            height=150,
            font=ctk.CTkFont(family="Consolas", size=10)
        )
        self.download_chart_text.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 用户活动图
        self.activity_chart_frame = ctk.CTkFrame(self.charts_frame)
        self.activity_chart_frame.grid(row=0, column=1, padx=10, pady=10, sticky="nsew")
        
        activity_title = ctk.CTkLabel(
            self.activity_chart_frame,
            text="👤 用户活动分布",
            font=self.main_window.title_font
        )
        activity_title.pack(pady=10)
        
        self.activity_chart_text = ctk.CTkTextbox(
            self.activity_chart_frame,
            height=150,
            font=ctk.CTkFont(family="Consolas", size=10)
        )
        self.activity_chart_text.pack(fill="both", expand=True, padx=10, pady=10)
        
    def create_activity_log(self):
        """创建活动日志区域"""
        # 活动日志容器
        self.activity_frame = ctk.CTkFrame(self.frame)
        self.activity_frame.pack(fill="x", padx=20, pady=10)
        
        # 标题
        activity_title = ctk.CTkLabel(
            self.activity_frame,
            text="📋 最近活动",
            font=self.main_window.title_font
        )
        activity_title.pack(pady=(10, 5), anchor="w", padx=10)
        
        # 活动列表
        self.activity_listbox = ctk.CTkTextbox(
            self.activity_frame,
            height=120,
            font=self.main_window.default_font
        )
        self.activity_listbox.pack(fill="x", padx=10, pady=10)
        
        # 添加一些示例活动
        self.add_activity("系统启动", "系统管理员", "INFO")
        self.add_activity("用户登录", "admin", "INFO")
        
    def create_system_info(self):
        """创建系统信息区域"""
        # 系统信息容器
        self.system_frame = ctk.CTkFrame(self.frame)
        self.system_frame.pack(fill="x", padx=20, pady=10)
        
        # 配置网格
        self.system_frame.grid_columnconfigure(0, weight=1)
        self.system_frame.grid_columnconfigure(1, weight=1)
        
        # 系统状态
        self.system_status_frame = ctk.CTkFrame(self.system_frame)
        self.system_status_frame.grid(row=0, column=0, padx=10, pady=10, sticky="nsew")
        
        status_title = ctk.CTkLabel(
            self.system_status_frame,
            text="🖥️ 系统状态",
            font=self.main_window.title_font
        )
        status_title.pack(pady=10)
        
        self.system_status_text = ctk.CTkTextbox(
            self.system_status_frame,
            height=100,
            font=self.main_window.default_font
        )
        self.system_status_text.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 快速操作
        self.quick_actions_frame = ctk.CTkFrame(self.system_frame)
        self.quick_actions_frame.grid(row=0, column=1, padx=10, pady=10, sticky="nsew")
        
        actions_title = ctk.CTkLabel(
            self.quick_actions_frame,
            text="⚡ 快速操作",
            font=self.main_window.title_font
        )
        actions_title.pack(pady=10)
        
        # 操作按钮
        self.refresh_btn = ctk.CTkButton(
            self.quick_actions_frame,
            text="🔄 刷新数据",
            command=self.refresh_data
        )
        self.refresh_btn.pack(pady=5, padx=10, fill="x")
        
        self.backup_btn = ctk.CTkButton(
            self.quick_actions_frame,
            text="💾 备份数据库",
            command=self.main_window.backup_database
        )
        self.backup_btn.pack(pady=5, padx=10, fill="x")
        
        self.export_logs_btn = ctk.CTkButton(
            self.quick_actions_frame,
            text="📤 导出日志",
            command=self.main_window.export_logs
        )
        self.export_logs_btn.pack(pady=5, padx=10, fill="x")
        
    def start_data_refresh(self):
        """启动数据刷新"""
        def refresh_loop():
            while True:
                if self.is_visible:
                    self.refresh_data()
                time.sleep(30)  # 每30秒刷新一次
                
        refresh_thread = threading.Thread(target=refresh_loop, daemon=True)
        refresh_thread.start()
        
    def refresh_data(self):
        """刷新数据"""
        try:
            # 更新统计卡片（这里使用模拟数据，实际项目中从数据库获取）
            import random
            
            # 在线用户
            online_users = random.randint(0, 50)
            self.online_users_card['value_label'].configure(text=str(online_users))
            
            # 文件总数
            total_files = random.randint(1000, 5000)
            self.total_files_card['value_label'].configure(text=str(total_files))
            
            # 今日下载
            today_downloads = random.randint(0, 200)
            self.today_downloads_card['value_label'].configure(text=str(today_downloads))
            
            # 存储使用
            storage_usage = random.randint(10, 100)
            self.storage_usage_card['value_label'].configure(text=str(storage_usage))
            
            # 更新图表
            self.update_charts()
            
            # 更新系统状态
            self.update_system_status()
            
        except Exception as e:
            print(f"刷新数据错误: {e}")
            
    def update_charts(self):
        """更新图表"""
        # 下载趋势图（简单文本图表）
        chart_data = """
日期        下载次数
2024-01-01    45 ████████████████████████████████████████████████
2024-01-02    32 ████████████████████████████████████
2024-01-03    58 ██████████████████████████████████████████████████████████████
2024-01-04    41 ████████████████████████████████████████████
2024-01-05    67 ███████████████████████████████████████████████████████████████████████
2024-01-06    29 ███████████████████████████████
2024-01-07    52 ████████████████████████████████████████████████████████
        """
        
        self.download_chart_text.delete("1.0", "end")
        self.download_chart_text.insert("1.0", chart_data)
        
        # 用户活动分布
        activity_data = """
时间段      活跃用户
00-06时     ██ 2
06-12时     ████████████ 12
12-18时     ████████████████████ 20
18-24时     ████████ 8

用户类型分布:
管理员      ██ 2
高级用户    ████ 4
普通用户    ████████████████ 16
只读用户    ████████ 8
        """
        
        self.activity_chart_text.delete("1.0", "end")
        self.activity_chart_text.insert("1.0", activity_data)
        
    def update_system_status(self):
        """更新系统状态"""
        import psutil
        
        # 获取系统信息
        cpu_percent = psutil.cpu_percent()
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        status_text = f"""
CPU 使用率: {cpu_percent}%
内存使用率: {memory.percent}%
磁盘使用率: {disk.percent}%

服务状态:
• Web服务: {'运行中' if self.main_window.server_running else '已停止'}
• 数据库: 连接正常
• 搜索引擎: 运行中
• 文件监控: 运行中

最后更新: {datetime.now().strftime('%H:%M:%S')}
        """
        
        self.system_status_text.delete("1.0", "end")
        self.system_status_text.insert("1.0", status_text)
        
    def add_activity(self, action, user, level):
        """添加活动记录"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        activity_text = f"[{timestamp}] {level} - {user}: {action}\n"
        
        self.activity_listbox.insert("end", activity_text)
        self.activity_listbox.see("end")
        
    def show(self):
        """显示页面"""
        self.frame.pack(fill="both", expand=True)
        self.is_visible = True
        self.refresh_data()
        
    def hide(self):
        """隐藏页面"""
        self.frame.pack_forget()
        self.is_visible = False
