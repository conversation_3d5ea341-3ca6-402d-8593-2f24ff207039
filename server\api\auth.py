"""
认证相关API
"""
from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import (
    create_access_token, create_refresh_token,
    jwt_required, get_jwt_identity, get_jwt
)
from datetime import datetime, timedelta
import uuid

from server.database.models import db, User, OnlineUser, SystemLog, LicenseKey
from server.core.utils import validate_password, get_client_ip

auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/login', methods=['POST'])
def login():
    """用户登录"""
    try:
        data = request.get_json()
        username = data.get('username', '').strip()
        password = data.get('password', '').strip()
        
        if not username or not password:
            return jsonify({
                'success': False,
                'message': '用户名和密码不能为空'
            }), 400
        
        # 查找用户
        user = User.query.filter_by(username=username).first()
        if not user or not user.check_password(password):
            # 记录登录失败日志
            log_system_event(
                'LOGIN_FAILED',
                f'用户 {username} 登录失败',
                user_id=user.id if user else None,
                client_ip=get_client_ip()
            )
            return jsonify({
                'success': False,
                'message': '用户名或密码错误'
            }), 401
        
        # 检查用户状态
        if user.status != 'active':
            return jsonify({
                'success': False,
                'message': '账户已被禁用'
            }), 403
        
        # 创建访问令牌
        access_token = create_access_token(
            identity=user.id,
            expires_delta=timedelta(seconds=current_app.config['JWT_ACCESS_TOKEN_EXPIRES'])
        )
        refresh_token = create_refresh_token(identity=user.id)
        
        # 更新用户登录信息
        user.last_login_time = datetime.utcnow()
        user.last_login_ip = get_client_ip()
        user.login_count += 1
        
        # 创建在线用户记录
        session_id = str(uuid.uuid4())
        online_user = OnlineUser(
            user_id=user.id,
            session_id=session_id,
            client_ip=get_client_ip(),
            user_agent=request.headers.get('User-Agent', '')
        )
        
        db.session.add(online_user)
        db.session.commit()
        
        # 记录登录成功日志
        log_system_event(
            'LOGIN_SUCCESS',
            f'用户 {username} 登录成功',
            user_id=user.id,
            client_ip=get_client_ip()
        )
        
        return jsonify({
            'success': True,
            'message': '登录成功',
            'data': {
                'access_token': access_token,
                'refresh_token': refresh_token,
                'user': {
                    'id': user.id,
                    'username': user.username,
                    'real_name': user.real_name,
                    'email': user.email,
                    'is_admin': user.is_admin(),
                    'group': user.group.group_name if user.group else None
                },
                'session_id': session_id
            }
        })
        
    except Exception as e:
        current_app.logger.error(f"登录错误: {e}")
        return jsonify({
            'success': False,
            'message': '登录失败，请稍后重试'
        }), 500

@auth_bp.route('/logout', methods=['POST'])
@jwt_required()
def logout():
    """用户登出"""
    try:
        user_id = get_jwt_identity()
        session_id = request.json.get('session_id') if request.json else None
        
        # 删除在线用户记录
        if session_id:
            online_user = OnlineUser.query.filter_by(
                user_id=user_id,
                session_id=session_id
            ).first()
            if online_user:
                db.session.delete(online_user)
        else:
            # 删除该用户的所有在线记录
            OnlineUser.query.filter_by(user_id=user_id).delete()
        
        db.session.commit()
        
        # 记录登出日志
        user = User.query.get(user_id)
        log_system_event(
            'LOGOUT',
            f'用户 {user.username if user else user_id} 登出',
            user_id=user_id,
            client_ip=get_client_ip()
        )
        
        return jsonify({
            'success': True,
            'message': '登出成功'
        })
        
    except Exception as e:
        current_app.logger.error(f"登出错误: {e}")
        return jsonify({
            'success': False,
            'message': '登出失败'
        }), 500

@auth_bp.route('/refresh', methods=['POST'])
@jwt_required(refresh=True)
def refresh():
    """刷新访问令牌"""
    try:
        user_id = get_jwt_identity()
        user = User.query.get(user_id)
        
        if not user or user.status != 'active':
            return jsonify({
                'success': False,
                'message': '用户状态异常'
            }), 403
        
        # 创建新的访问令牌
        access_token = create_access_token(
            identity=user_id,
            expires_delta=timedelta(seconds=current_app.config['JWT_ACCESS_TOKEN_EXPIRES'])
        )
        
        return jsonify({
            'success': True,
            'data': {
                'access_token': access_token
            }
        })
        
    except Exception as e:
        current_app.logger.error(f"刷新令牌错误: {e}")
        return jsonify({
            'success': False,
            'message': '刷新令牌失败'
        }), 500

@auth_bp.route('/register', methods=['POST'])
def register():
    """用户注册"""
    try:
        data = request.get_json()
        username = data.get('username', '').strip()
        password = data.get('password', '').strip()
        email = data.get('email', '').strip()
        real_name = data.get('real_name', '').strip()
        license_key = data.get('license_key', '').strip()
        
        # 验证必填字段
        if not all([username, password, license_key]):
            return jsonify({
                'success': False,
                'message': '用户名、密码和注册码不能为空'
            }), 400
        
        # 验证密码强度
        if not validate_password(password):
            return jsonify({
                'success': False,
                'message': f'密码长度至少{current_app.config["PASSWORD_MIN_LENGTH"]}位'
            }), 400
        
        # 检查用户名是否已存在
        if User.query.filter_by(username=username).first():
            return jsonify({
                'success': False,
                'message': '用户名已存在'
            }), 400
        
        # 检查邮箱是否已存在
        if email and User.query.filter_by(email=email).first():
            return jsonify({
                'success': False,
                'message': '邮箱已被使用'
            }), 400
        
        # 验证注册码
        license = LicenseKey.query.filter_by(license_key=license_key).first()
        if not license or not license.is_valid():
            return jsonify({
                'success': False,
                'message': '注册码无效或已过期'
            }), 400
        
        # 创建用户
        user = User(
            username=username,
            email=email,
            real_name=real_name,
            user_group_id=3  # 默认为普通用户组
        )
        user.set_password(password)
        
        # 更新注册码使用情况
        license.used_users += 1
        if not license.used_at:
            license.used_at = datetime.utcnow()
        
        db.session.add(user)
        db.session.commit()
        
        # 记录注册日志
        log_system_event(
            'USER_REGISTER',
            f'新用户 {username} 注册成功',
            user_id=user.id,
            client_ip=get_client_ip()
        )
        
        return jsonify({
            'success': True,
            'message': '注册成功',
            'data': {
                'user_id': user.id,
                'username': user.username
            }
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"注册错误: {e}")
        return jsonify({
            'success': False,
            'message': '注册失败，请稍后重试'
        }), 500

@auth_bp.route('/profile', methods=['GET'])
@jwt_required()
def get_profile():
    """获取用户信息"""
    try:
        user_id = get_jwt_identity()
        user = User.query.get(user_id)
        
        if not user:
            return jsonify({
                'success': False,
                'message': '用户不存在'
            }), 404
        
        return jsonify({
            'success': True,
            'data': {
                'id': user.id,
                'username': user.username,
                'real_name': user.real_name,
                'email': user.email,
                'status': user.status,
                'is_admin': user.is_admin(),
                'group': user.group.group_name if user.group else None,
                'last_login_time': user.last_login_time.isoformat() if user.last_login_time else None,
                'login_count': user.login_count,
                'created_at': user.created_at.isoformat()
            }
        })
        
    except Exception as e:
        current_app.logger.error(f"获取用户信息错误: {e}")
        return jsonify({
            'success': False,
            'message': '获取用户信息失败'
        }), 500

@auth_bp.route('/change-password', methods=['POST'])
@jwt_required()
def change_password():
    """修改密码"""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()
        old_password = data.get('old_password', '').strip()
        new_password = data.get('new_password', '').strip()
        
        if not old_password or not new_password:
            return jsonify({
                'success': False,
                'message': '旧密码和新密码不能为空'
            }), 400
        
        user = User.query.get(user_id)
        if not user or not user.check_password(old_password):
            return jsonify({
                'success': False,
                'message': '旧密码错误'
            }), 400
        
        # 验证新密码强度
        if not validate_password(new_password):
            return jsonify({
                'success': False,
                'message': f'新密码长度至少{current_app.config["PASSWORD_MIN_LENGTH"]}位'
            }), 400
        
        # 更新密码
        user.set_password(new_password)
        db.session.commit()
        
        # 记录密码修改日志
        log_system_event(
            'PASSWORD_CHANGE',
            f'用户 {user.username} 修改密码',
            user_id=user_id,
            client_ip=get_client_ip()
        )
        
        return jsonify({
            'success': True,
            'message': '密码修改成功'
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"修改密码错误: {e}")
        return jsonify({
            'success': False,
            'message': '修改密码失败'
        }), 500

def log_system_event(event_type, message, user_id=None, client_ip=None, level='INFO'):
    """记录系统事件日志"""
    try:
        log = SystemLog(
            log_level=level,
            log_type=event_type,
            user_id=user_id,
            message=message,
            client_ip=client_ip or get_client_ip()
        )
        db.session.add(log)
        db.session.commit()
    except Exception as e:
        current_app.logger.error(f"记录系统日志错误: {e}")
        db.session.rollback()
