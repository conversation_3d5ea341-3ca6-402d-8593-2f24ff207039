"""
搜索相关API
"""
from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from sqlalchemy import or_, and_

from server.database.models import db, FileIndex, SearchLog
from server.core.utils import get_client_ip, check_file_permission, compare_image_features

search_bp = Blueprint('search', __name__)

@search_bp.route('/files', methods=['GET'])
@jwt_required()
def search_files():
    """搜索文件"""
    try:
        user_id = get_jwt_identity()
        keyword = request.args.get('keyword', '').strip()
        search_type = request.args.get('type', 'filename')  # filename, image, mixed
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        
        if not keyword:
            return jsonify({
                'success': False,
                'message': '搜索关键词不能为空'
            }), 400
        
        # 构建基础查询
        query = FileIndex.query
        
        if search_type == 'filename':
            # 文件名搜索
            query = query.filter(
                or_(
                    FileIndex.file_name.contains(keyword),
                    FileIndex.file_path.contains(keyword)
                )
            )
        elif search_type == 'image':
            # 图像搜索（基于特征）
            # 这里需要实现图像特征搜索逻辑
            return jsonify({
                'success': False,
                'message': '图像搜索功能开发中'
            }), 501
        else:
            # 混合搜索
            query = query.filter(
                or_(
                    FileIndex.file_name.contains(keyword),
                    FileIndex.file_path.contains(keyword)
                )
            )
        
        # 分页
        pagination = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )
        
        # 过滤用户有权限访问的文件
        results = []
        for file in pagination.items:
            if check_file_permission(user_id, file.shared_dir_id, 'read'):
                results.append({
                    'id': file.id,
                    'name': file.file_name,
                    'path': file.file_path,
                    'size': file.file_size,
                    'type': file.file_type,
                    'has_thumbnail': file.has_thumbnail,
                    'last_modified': file.last_modified.isoformat() if file.last_modified else None,
                    'directory': file.shared_directory.name
                })
        
        # 记录搜索日志
        search_log = SearchLog(
            user_id=user_id,
            search_keyword=keyword,
            search_type=search_type,
            result_count=len(results),
            client_ip=get_client_ip()
        )
        db.session.add(search_log)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'data': {
                'files': results,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': len(results),
                    'pages': (len(results) + per_page - 1) // per_page
                },
                'search_info': {
                    'keyword': keyword,
                    'type': search_type,
                    'result_count': len(results)
                }
            }
        })
        
    except Exception as e:
        current_app.logger.error(f"搜索文件错误: {e}")
        return jsonify({
            'success': False,
            'message': '搜索失败'
        }), 500

@search_bp.route('/suggestions', methods=['GET'])
@jwt_required()
def get_search_suggestions():
    """获取搜索建议"""
    try:
        user_id = get_jwt_identity()
        keyword = request.args.get('keyword', '').strip()
        
        if not keyword or len(keyword) < 2:
            return jsonify({
                'success': True,
                'data': []
            })
        
        # 获取文件名建议
        suggestions = db.session.query(FileIndex.file_name).filter(
            FileIndex.file_name.contains(keyword)
        ).distinct().limit(10).all()
        
        result = [s[0] for s in suggestions]
        
        return jsonify({
            'success': True,
            'data': result
        })
        
    except Exception as e:
        current_app.logger.error(f"获取搜索建议错误: {e}")
        return jsonify({
            'success': False,
            'message': '获取建议失败'
        }), 500
