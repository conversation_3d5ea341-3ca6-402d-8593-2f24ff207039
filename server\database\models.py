"""
数据库模型定义
"""
from datetime import datetime
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
import json

db = SQLAlchemy()

class SystemConfig(db.Model):
    """系统配置表"""
    __tablename__ = 'system_config'
    
    id = db.Column(db.Integer, primary_key=True)
    config_key = db.Column(db.String(100), unique=True, nullable=False, comment='配置键')
    config_value = db.Column(db.Text, comment='配置值')
    description = db.Column(db.String(255), comment='配置描述')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f'<SystemConfig {self.config_key}>'

class UserGroup(db.Model):
    """用户组表"""
    __tablename__ = 'user_groups'
    
    id = db.Column(db.Integer, primary_key=True)
    group_name = db.Column(db.String(50), unique=True, nullable=False, comment='组名')
    description = db.Column(db.String(255), comment='组描述')
    permissions = db.Column(db.JSON, comment='权限配置')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    users = db.relationship('User', backref='group', lazy='dynamic')
    
    def __repr__(self):
        return f'<UserGroup {self.group_name}>'
    
    def get_permissions(self):
        """获取权限配置"""
        return self.permissions or {}
    
    def set_permissions(self, permissions_dict):
        """设置权限配置"""
        self.permissions = permissions_dict

class User(db.Model):
    """用户表"""
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(50), unique=True, nullable=False, comment='用户名')
    password_hash = db.Column(db.String(255), nullable=False, comment='密码哈希')
    email = db.Column(db.String(100), comment='邮箱')
    real_name = db.Column(db.String(100), comment='真实姓名')
    user_group_id = db.Column(db.Integer, db.ForeignKey('user_groups.id'), comment='用户组ID')
    status = db.Column(db.Enum('active', 'disabled', 'banned'), default='active', comment='用户状态')
    last_login_time = db.Column(db.DateTime, comment='最后登录时间')
    last_login_ip = db.Column(db.String(45), comment='最后登录IP')
    login_count = db.Column(db.Integer, default=0, comment='登录次数')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    permissions = db.relationship('UserPermission', backref='user', lazy='dynamic', cascade='all, delete-orphan')
    download_logs = db.relationship('DownloadLog', backref='user', lazy='dynamic')
    search_logs = db.relationship('SearchLog', backref='user', lazy='dynamic')
    system_logs = db.relationship('SystemLog', backref='user', lazy='dynamic')
    online_sessions = db.relationship('OnlineUser', backref='user', lazy='dynamic', cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<User {self.username}>'
    
    def set_password(self, password):
        """设置密码"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """验证密码"""
        return check_password_hash(self.password_hash, password)
    
    def is_admin(self):
        """是否为管理员"""
        if self.group:
            permissions = self.group.get_permissions()
            return permissions.get('admin', False)
        return False
    
    def get_permissions_for_directory(self, shared_dir_id):
        """获取用户对指定目录的权限"""
        permission = self.permissions.filter_by(shared_dir_id=shared_dir_id).first()
        if permission:
            return {
                'read': permission.can_read,
                'write': permission.can_write,
                'delete': permission.can_delete,
                'download': permission.can_download,
                'upload': permission.can_upload
            }
        
        # 如果没有特定权限，使用用户组默认权限
        if self.group:
            group_permissions = self.group.get_permissions()
            return {
                'read': group_permissions.get('read', True),
                'write': group_permissions.get('write', False),
                'delete': group_permissions.get('delete', False),
                'download': group_permissions.get('download', True),
                'upload': group_permissions.get('upload', False)
            }
        
        # 默认只读权限
        return {
            'read': True,
            'write': False,
            'delete': False,
            'download': False,
            'upload': False
        }

class SharedDirectory(db.Model):
    """共享目录表"""
    __tablename__ = 'shared_directories'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, comment='目录名称')
    path = db.Column(db.String(500), nullable=False, comment='物理路径')
    description = db.Column(db.String(255), comment='目录描述')
    is_active = db.Column(db.Boolean, default=True, comment='是否启用')
    allow_internal = db.Column(db.Boolean, default=True, comment='允许内网访问')
    allow_external = db.Column(db.Boolean, default=False, comment='允许外网访问')
    default_permissions = db.Column(db.JSON, comment='默认权限')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    files = db.relationship('FileIndex', backref='shared_directory', lazy='dynamic', cascade='all, delete-orphan')
    user_permissions = db.relationship('UserPermission', backref='shared_directory', lazy='dynamic', cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<SharedDirectory {self.name}>'
    
    def get_default_permissions(self):
        """获取默认权限"""
        return self.default_permissions or {
            'read': True,
            'write': False,
            'delete': False,
            'download': True,
            'upload': False
        }

class FileIndex(db.Model):
    """文件索引表"""
    __tablename__ = 'file_index'
    
    id = db.Column(db.BigInteger, primary_key=True)
    shared_dir_id = db.Column(db.Integer, db.ForeignKey('shared_directories.id'), nullable=False, comment='共享目录ID')
    file_path = db.Column(db.String(1000), nullable=False, comment='文件相对路径')
    file_name = db.Column(db.String(255), nullable=False, comment='文件名')
    file_size = db.Column(db.BigInteger, comment='文件大小')
    file_type = db.Column(db.String(50), comment='文件类型')
    file_hash = db.Column(db.String(64), comment='文件哈希值')
    has_thumbnail = db.Column(db.Boolean, default=False, comment='是否有缩略图')
    thumbnail_path = db.Column(db.String(500), comment='缩略图路径')
    image_features = db.Column(db.JSON, comment='图像特征数据')
    last_modified = db.Column(db.DateTime, comment='文件最后修改时间')
    indexed_at = db.Column(db.DateTime, default=datetime.utcnow, comment='索引时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    download_logs = db.relationship('DownloadLog', backref='file', lazy='dynamic')
    
    def __repr__(self):
        return f'<FileIndex {self.file_name}>'
    
    def get_full_path(self):
        """获取完整文件路径"""
        return f"{self.shared_directory.path}/{self.file_path}"
    
    def get_image_features(self):
        """获取图像特征"""
        return self.image_features or {}
    
    def set_image_features(self, features):
        """设置图像特征"""
        self.image_features = features

class UserPermission(db.Model):
    """用户权限表"""
    __tablename__ = 'user_permissions'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, comment='用户ID')
    shared_dir_id = db.Column(db.Integer, db.ForeignKey('shared_directories.id'), nullable=False, comment='共享目录ID')
    can_read = db.Column(db.Boolean, default=True, comment='读取权限')
    can_write = db.Column(db.Boolean, default=False, comment='写入权限')
    can_delete = db.Column(db.Boolean, default=False, comment='删除权限')
    can_download = db.Column(db.Boolean, default=True, comment='下载权限')
    can_upload = db.Column(db.Boolean, default=False, comment='上传权限')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    __table_args__ = (db.UniqueConstraint('user_id', 'shared_dir_id', name='uk_user_dir'),)
    
    def __repr__(self):
        return f'<UserPermission user:{self.user_id} dir:{self.shared_dir_id}>'

class DownloadLog(db.Model):
    """下载记录表"""
    __tablename__ = 'download_logs'
    
    id = db.Column(db.BigInteger, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, comment='用户ID')
    file_id = db.Column(db.BigInteger, db.ForeignKey('file_index.id'), comment='文件ID')
    file_path = db.Column(db.String(1000), comment='文件路径')
    download_type = db.Column(db.Enum('single', 'batch', 'folder'), comment='下载类型')
    file_count = db.Column(db.Integer, default=1, comment='文件数量')
    total_size = db.Column(db.BigInteger, comment='总大小')
    is_encrypted = db.Column(db.Boolean, default=False, comment='是否加密')
    password_requested = db.Column(db.Boolean, default=False, comment='是否申请密码')
    client_ip = db.Column(db.String(45), comment='客户端IP')
    user_agent = db.Column(db.Text, comment='用户代理')
    download_time = db.Column(db.DateTime, default=datetime.utcnow, comment='下载时间')
    
    def __repr__(self):
        return f'<DownloadLog user:{self.user_id} file:{self.file_id}>'

class SearchLog(db.Model):
    """搜索记录表"""
    __tablename__ = 'search_logs'
    
    id = db.Column(db.BigInteger, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, comment='用户ID')
    search_keyword = db.Column(db.String(500), comment='搜索关键词')
    search_type = db.Column(db.Enum('filename', 'image', 'mixed'), comment='搜索类型')
    result_count = db.Column(db.Integer, default=0, comment='结果数量')
    search_time = db.Column(db.DateTime, default=datetime.utcnow, comment='搜索时间')
    client_ip = db.Column(db.String(45), comment='客户端IP')
    
    def __repr__(self):
        return f'<SearchLog user:{self.user_id} keyword:{self.search_keyword}>'

class SystemLog(db.Model):
    """系统日志表"""
    __tablename__ = 'system_logs'
    
    id = db.Column(db.BigInteger, primary_key=True)
    log_level = db.Column(db.Enum('INFO', 'WARNING', 'ERROR', 'CRITICAL'), default='INFO')
    log_type = db.Column(db.String(50), comment='日志类型')
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), comment='相关用户ID')
    message = db.Column(db.Text, comment='日志消息')
    details = db.Column(db.JSON, comment='详细信息')
    client_ip = db.Column(db.String(45), comment='客户端IP')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def __repr__(self):
        return f'<SystemLog {self.log_level}:{self.log_type}>'

class LicenseKey(db.Model):
    """注册码表"""
    __tablename__ = 'license_keys'
    
    id = db.Column(db.Integer, primary_key=True)
    license_key = db.Column(db.String(100), unique=True, nullable=False, comment='注册码')
    max_users = db.Column(db.Integer, default=1, comment='最大用户数')
    used_users = db.Column(db.Integer, default=0, comment='已使用用户数')
    expire_date = db.Column(db.Date, comment='过期日期')
    is_active = db.Column(db.Boolean, default=True, comment='是否有效')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    used_at = db.Column(db.DateTime, comment='使用时间')
    
    def __repr__(self):
        return f'<LicenseKey {self.license_key}>'
    
    def is_valid(self):
        """检查注册码是否有效"""
        if not self.is_active:
            return False
        if self.expire_date and self.expire_date < datetime.utcnow().date():
            return False
        if self.used_users >= self.max_users:
            return False
        return True

class OnlineUser(db.Model):
    """在线用户表"""
    __tablename__ = 'online_users'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, comment='用户ID')
    session_id = db.Column(db.String(100), unique=True, nullable=False, comment='会话ID')
    client_ip = db.Column(db.String(45), comment='客户端IP')
    user_agent = db.Column(db.Text, comment='用户代理')
    last_activity = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    login_time = db.Column(db.DateTime, default=datetime.utcnow)
    
    def __repr__(self):
        return f'<OnlineUser {self.user.username}>'
