"""
文件管理页面
"""
import customtkinter as ctk
from tkinter import ttk, messagebox, filedialog
from server.config.config import GUIConfig

class FilesPage:
    """文件管理页面类"""
    
    def __init__(self, parent, main_window):
        self.parent = parent
        self.main_window = main_window
        self.is_visible = False
        
        self.create_widgets()
        
    def create_widgets(self):
        """创建页面组件"""
        # 创建主框架
        self.frame = ctk.CTkFrame(self.parent)
        
        # 页面标题
        self.title_label = ctk.CTkLabel(
            self.frame,
            text="📁 文件管理",
            font=self.main_window.title_font,
            anchor="w"
        )
        self.title_label.pack(fill="x", padx=20, pady=(20, 10))
        
        # 工具栏
        self.toolbar_frame = ctk.CTkFrame(self.frame)
        self.toolbar_frame.pack(fill="x", padx=20, pady=10)
        
        self.add_share_btn = ctk.CTkButton(
            self.toolbar_frame,
            text="➕ 添加共享目录",
            command=self.add_shared_directory,
            fg_color=GUIConfig.COLORS["success"]
        )
        self.add_share_btn.pack(side="left", padx=10, pady=10)
        
        self.refresh_btn = ctk.CTkButton(
            self.toolbar_frame,
            text="🔄 刷新索引",
            command=self.refresh_index
        )
        self.refresh_btn.pack(side="left", padx=10, pady=10)
        
        # 主内容区域
        self.content_frame = ctk.CTkFrame(self.frame)
        self.content_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # 共享目录列表
        self.create_directory_list()
        
    def create_directory_list(self):
        """创建共享目录列表"""
        # 目录列表标题
        list_title = ctk.CTkLabel(
            self.content_frame,
            text="共享目录列表",
            font=self.main_window.title_font
        )
        list_title.pack(pady=10)
        
        # 目录表格
        columns = ("名称", "路径", "状态", "内网访问", "外网访问", "文件数量", "总大小")
        self.dir_tree = ttk.Treeview(self.content_frame, columns=columns, show="headings", height=15)
        
        for col in columns:
            self.dir_tree.heading(col, text=col)
            self.dir_tree.column(col, width=120)
            
        self.dir_tree.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 加载示例数据
        self.load_directories()
        
    def add_shared_directory(self):
        """添加共享目录"""
        directory = filedialog.askdirectory(title="选择要共享的目录")
        if directory:
            messagebox.showinfo("添加共享目录", f"共享目录功能开发中\n选择的目录: {directory}")
            
    def refresh_index(self):
        """刷新文件索引"""
        messagebox.showinfo("刷新索引", "文件索引刷新功能开发中")
        
    def load_directories(self):
        """加载共享目录"""
        # 示例数据
        directories = [
            ("图片库", "D:/Images", "启用", "是", "否", "1,234", "2.5 GB"),
            ("文档库", "D:/Documents", "启用", "是", "是", "567", "1.2 GB"),
            ("设计素材", "E:/Design", "禁用", "是", "否", "890", "5.8 GB"),
        ]
        
        for directory in directories:
            self.dir_tree.insert("", "end", values=directory)
            
    def show(self):
        """显示页面"""
        self.frame.pack(fill="both", expand=True)
        self.is_visible = True
        
    def hide(self):
        """隐藏页面"""
        self.frame.pack_forget()
        self.is_visible = False
