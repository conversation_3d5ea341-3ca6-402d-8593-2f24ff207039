@echo off
chcp 65001 >nul
title 企业文件共享系统 - 服务端

echo.
echo ========================================
echo    企业文件共享系统 - 服务端启动器
echo ========================================
echo.

:MENU
echo 请选择启动模式:
echo.
echo [1] GUI管理界面 (推荐)
echo [2] API服务模式
echo [3] 检查系统依赖
echo [4] 初始化数据库
echo [5] 安装系统
echo [6] 退出
echo.
set /p choice=请输入选项 (1-6): 

if "%choice%"=="1" goto GUI_MODE
if "%choice%"=="2" goto API_MODE
if "%choice%"=="3" goto CHECK_DEPS
if "%choice%"=="4" goto INIT_DB
if "%choice%"=="5" goto INSTALL
if "%choice%"=="6" goto EXIT
echo 无效选项，请重新选择
goto MENU

:GUI_MODE
echo.
echo 正在启动GUI管理界面...
echo.
python run_server.py gui
if errorlevel 1 (
    echo.
    echo ❌ GUI启动失败，请检查Python环境和依赖
    pause
)
goto MENU

:API_MODE
echo.
echo 正在启动API服务...
echo 服务将在 http://localhost:5000 运行
echo 按 Ctrl+C 停止服务
echo.
python run_server.py api
if errorlevel 1 (
    echo.
    echo ❌ API服务启动失败，请检查配置
    pause
)
goto MENU

:CHECK_DEPS
echo.
echo 正在检查系统依赖...
echo.
python run_server.py check
echo.
pause
goto MENU

:INIT_DB
echo.
echo 正在初始化数据库...
echo.
python run_server.py init-db
echo.
pause
goto MENU

:INSTALL
echo.
echo 正在运行安装程序...
echo.
python install.py
echo.
pause
goto MENU

:EXIT
echo.
echo 感谢使用企业文件共享系统！
echo.
pause
exit

:ERROR
echo.
echo ❌ 发生错误，请检查:
echo 1. Python是否已正确安装
echo 2. 是否在正确的目录中运行
echo 3. 依赖包是否已安装
echo.
pause
goto MENU
