"""
侧边栏组件
"""
import customtkinter as ctk
from server.config.config import GUIConfig

class Sidebar:
    """侧边栏类"""
    
    def __init__(self, parent, main_window):
        self.parent = parent
        self.main_window = main_window
        self.buttons = {}
        self.active_button = None
        
        self.create_widgets()
        
    def create_widgets(self):
        """创建侧边栏组件"""
        # 创建侧边栏框架
        self.sidebar_frame = ctk.CTkFrame(
            self.parent,
            width=GUIConfig.SIDEBAR_WIDTH,
            corner_radius=10
        )
        self.sidebar_frame.pack(side="left", fill="y", padx=(0, 5))
        self.sidebar_frame.pack_propagate(False)
        
        # 创建标题
        self.title_label = ctk.CTkLabel(
            self.sidebar_frame,
            text="系统管理",
            font=self.main_window.title_font,
            text_color=GUIConfig.COLORS["primary"]
        )
        self.title_label.pack(pady=(20, 30))
        
        # 创建导航按钮
        self.create_nav_buttons()
        
        # 创建底部信息
        self.create_bottom_info()
        
    def create_nav_buttons(self):
        """创建导航按钮"""
        nav_items = [
            ("dashboard", "📊 仪表板", "查看系统概览和统计信息"),
            ("users", "👥 用户管理", "管理用户账户和权限"),
            ("files", "📁 文件管理", "管理共享文件和目录"),
            ("monitor", "📈 实时监控", "查看系统运行状态"),
            ("logs", "📋 日志管理", "查看系统日志和用户行为"),
            ("settings", "⚙️ 系统设置", "配置系统参数和选项")
        ]
        
        for page_id, text, tooltip in nav_items:
            button = ctk.CTkButton(
                self.sidebar_frame,
                text=text,
                width=GUIConfig.SIDEBAR_WIDTH - 40,
                height=40,
                font=self.main_window.default_font,
                command=lambda p=page_id: self.on_button_click(p),
                anchor="w",
                corner_radius=8
            )
            button.pack(pady=5, padx=20)
            
            # 添加工具提示
            self.create_tooltip(button, tooltip)
            
            self.buttons[page_id] = button
            
    def create_bottom_info(self):
        """创建底部信息区域"""
        # 创建分隔线
        separator = ctk.CTkFrame(
            self.sidebar_frame,
            height=2,
            fg_color=GUIConfig.COLORS["light"]
        )
        separator.pack(fill="x", padx=20, pady=20)
        
        # 服务器状态
        self.server_status_frame = ctk.CTkFrame(self.sidebar_frame)
        self.server_status_frame.pack(fill="x", padx=20, pady=10)
        
        self.server_status_label = ctk.CTkLabel(
            self.server_status_frame,
            text="🔴 服务器离线",
            font=self.main_window.default_font
        )
        self.server_status_label.pack(pady=10)
        
        # 快速操作按钮
        self.quick_actions_frame = ctk.CTkFrame(self.sidebar_frame)
        self.quick_actions_frame.pack(fill="x", padx=20, pady=10)
        
        self.start_server_btn = ctk.CTkButton(
            self.quick_actions_frame,
            text="启动服务",
            width=80,
            height=30,
            font=ctk.CTkFont(size=10),
            command=self.main_window.start_server,
            fg_color=GUIConfig.COLORS["success"]
        )
        self.start_server_btn.pack(side="left", padx=5, pady=5)
        
        self.stop_server_btn = ctk.CTkButton(
            self.quick_actions_frame,
            text="停止服务",
            width=80,
            height=30,
            font=ctk.CTkFont(size=10),
            command=self.main_window.stop_server,
            fg_color=GUIConfig.COLORS["danger"]
        )
        self.stop_server_btn.pack(side="right", padx=5, pady=5)
        
        # 版本信息
        from server.config.config import Config
        self.version_label = ctk.CTkLabel(
            self.sidebar_frame,
            text=f"版本 {Config.VERSION}",
            font=ctk.CTkFont(size=10),
            text_color="gray"
        )
        self.version_label.pack(side="bottom", pady=10)
        
    def create_tooltip(self, widget, text):
        """创建工具提示"""
        def on_enter(event):
            tooltip = ctk.CTkToplevel()
            tooltip.wm_overrideredirect(True)
            tooltip.wm_geometry(f"+{event.x_root+10}+{event.y_root+10}")
            
            label = ctk.CTkLabel(
                tooltip,
                text=text,
                font=ctk.CTkFont(size=10),
                fg_color="black",
                text_color="white",
                corner_radius=5
            )
            label.pack(padx=5, pady=2)
            
            widget.tooltip = tooltip
            
        def on_leave(event):
            if hasattr(widget, 'tooltip'):
                widget.tooltip.destroy()
                delattr(widget, 'tooltip')
                
        widget.bind("<Enter>", on_enter)
        widget.bind("<Leave>", on_leave)
        
    def on_button_click(self, page_id):
        """按钮点击事件处理"""
        self.main_window.show_page(page_id)
        
    def set_active_button(self, page_id):
        """设置活动按钮"""
        # 重置所有按钮样式
        for btn_id, button in self.buttons.items():
            if btn_id == page_id:
                button.configure(
                    fg_color=GUIConfig.COLORS["primary"],
                    hover_color=GUIConfig.COLORS["secondary"]
                )
            else:
                button.configure(
                    fg_color="transparent",
                    hover_color=("gray75", "gray25")
                )
                
        self.active_button = page_id
        
    def update_server_status(self, is_running):
        """更新服务器状态显示"""
        if is_running:
            self.server_status_label.configure(
                text="🟢 服务器在线",
                text_color=GUIConfig.COLORS["success"]
            )
            self.start_server_btn.configure(state="disabled")
            self.stop_server_btn.configure(state="normal")
        else:
            self.server_status_label.configure(
                text="🔴 服务器离线",
                text_color=GUIConfig.COLORS["danger"]
            )
            self.start_server_btn.configure(state="normal")
            self.stop_server_btn.configure(state="disabled")
