"""
用户管理页面
"""
import customtkinter as ctk
from tkinter import ttk, messagebox
from server.config.config import GUIConfig

class UsersPage:
    """用户管理页面类"""
    
    def __init__(self, parent, main_window):
        self.parent = parent
        self.main_window = main_window
        self.is_visible = False
        self.selected_user = None
        
        self.create_widgets()
        self.load_users()
        
    def create_widgets(self):
        """创建页面组件"""
        # 创建主框架
        self.frame = ctk.CTkFrame(self.parent)
        
        # 页面标题
        self.title_label = ctk.CTkLabel(
            self.frame,
            text="👥 用户管理",
            font=self.main_window.title_font,
            anchor="w"
        )
        self.title_label.pack(fill="x", padx=20, pady=(20, 10))
        
        # 创建工具栏
        self.create_toolbar()
        
        # 创建主要内容区域
        self.create_main_content()
        
    def create_toolbar(self):
        """创建工具栏"""
        self.toolbar_frame = ctk.CTkFrame(self.frame)
        self.toolbar_frame.pack(fill="x", padx=20, pady=10)
        
        # 搜索框
        self.search_frame = ctk.CTkFrame(self.toolbar_frame, fg_color="transparent")
        self.search_frame.pack(side="left", fill="x", expand=True)
        
        self.search_entry = ctk.CTkEntry(
            self.search_frame,
            placeholder_text="搜索用户名、邮箱或真实姓名...",
            width=300
        )
        self.search_entry.pack(side="left", padx=(0, 10))
        
        self.search_btn = ctk.CTkButton(
            self.search_frame,
            text="🔍 搜索",
            width=80,
            command=self.search_users
        )
        self.search_btn.pack(side="left")
        
        # 操作按钮
        self.actions_frame = ctk.CTkFrame(self.toolbar_frame, fg_color="transparent")
        self.actions_frame.pack(side="right")
        
        self.add_user_btn = ctk.CTkButton(
            self.actions_frame,
            text="➕ 添加用户",
            command=self.add_user,
            fg_color=GUIConfig.COLORS["success"]
        )
        self.add_user_btn.pack(side="right", padx=5)
        
        self.refresh_btn = ctk.CTkButton(
            self.actions_frame,
            text="🔄 刷新",
            width=80,
            command=self.load_users
        )
        self.refresh_btn.pack(side="right", padx=5)
        
    def create_main_content(self):
        """创建主要内容区域"""
        # 主内容框架
        self.content_frame = ctk.CTkFrame(self.frame)
        self.content_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # 配置网格
        self.content_frame.grid_columnconfigure(0, weight=2)
        self.content_frame.grid_columnconfigure(1, weight=1)
        self.content_frame.grid_rowconfigure(0, weight=1)
        
        # 用户列表区域
        self.create_user_list()
        
        # 用户详情区域
        self.create_user_details()
        
    def create_user_list(self):
        """创建用户列表"""
        # 用户列表框架
        self.list_frame = ctk.CTkFrame(self.content_frame)
        self.list_frame.grid(row=0, column=0, padx=(0, 10), pady=10, sticky="nsew")
        
        # 列表标题
        list_title = ctk.CTkLabel(
            self.list_frame,
            text="用户列表",
            font=self.main_window.title_font
        )
        list_title.pack(pady=10)
        
        # 创建Treeview用于显示用户列表
        self.tree_frame = ctk.CTkFrame(self.list_frame)
        self.tree_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 用户表格
        columns = ("用户名", "真实姓名", "邮箱", "用户组", "状态", "最后登录")
        self.user_tree = ttk.Treeview(self.tree_frame, columns=columns, show="headings", height=15)
        
        # 设置列标题
        for col in columns:
            self.user_tree.heading(col, text=col)
            self.user_tree.column(col, width=100)
            
        # 滚动条
        scrollbar = ttk.Scrollbar(self.tree_frame, orient="vertical", command=self.user_tree.yview)
        self.user_tree.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        self.user_tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 绑定选择事件
        self.user_tree.bind("<<TreeviewSelect>>", self.on_user_select)
        
        # 右键菜单
        self.create_context_menu()
        
    def create_user_details(self):
        """创建用户详情区域"""
        # 详情框架
        self.details_frame = ctk.CTkFrame(self.content_frame)
        self.details_frame.grid(row=0, column=1, padx=(10, 0), pady=10, sticky="nsew")
        
        # 详情标题
        details_title = ctk.CTkLabel(
            self.details_frame,
            text="用户详情",
            font=self.main_window.title_font
        )
        details_title.pack(pady=10)
        
        # 用户信息表单
        self.form_frame = ctk.CTkScrollableFrame(self.details_frame)
        self.form_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 用户名
        self.username_label = ctk.CTkLabel(self.form_frame, text="用户名:")
        self.username_label.pack(anchor="w", pady=(10, 0))
        self.username_entry = ctk.CTkEntry(self.form_frame, width=200)
        self.username_entry.pack(fill="x", pady=(0, 10))
        
        # 真实姓名
        self.realname_label = ctk.CTkLabel(self.form_frame, text="真实姓名:")
        self.realname_label.pack(anchor="w", pady=(10, 0))
        self.realname_entry = ctk.CTkEntry(self.form_frame, width=200)
        self.realname_entry.pack(fill="x", pady=(0, 10))
        
        # 邮箱
        self.email_label = ctk.CTkLabel(self.form_frame, text="邮箱:")
        self.email_label.pack(anchor="w", pady=(10, 0))
        self.email_entry = ctk.CTkEntry(self.form_frame, width=200)
        self.email_entry.pack(fill="x", pady=(0, 10))
        
        # 用户组
        self.group_label = ctk.CTkLabel(self.form_frame, text="用户组:")
        self.group_label.pack(anchor="w", pady=(10, 0))
        self.group_combo = ctk.CTkComboBox(
            self.form_frame,
            values=["管理员", "高级用户", "普通用户", "只读用户"],
            width=200
        )
        self.group_combo.pack(fill="x", pady=(0, 10))
        
        # 状态
        self.status_label = ctk.CTkLabel(self.form_frame, text="状态:")
        self.status_label.pack(anchor="w", pady=(10, 0))
        self.status_combo = ctk.CTkComboBox(
            self.form_frame,
            values=["active", "disabled", "banned"],
            width=200
        )
        self.status_combo.pack(fill="x", pady=(0, 10))
        
        # 密码（仅在添加用户时显示）
        self.password_label = ctk.CTkLabel(self.form_frame, text="密码:")
        self.password_entry = ctk.CTkEntry(self.form_frame, width=200, show="*")
        
        # 操作按钮
        self.buttons_frame = ctk.CTkFrame(self.form_frame, fg_color="transparent")
        self.buttons_frame.pack(fill="x", pady=20)
        
        self.save_btn = ctk.CTkButton(
            self.buttons_frame,
            text="💾 保存",
            command=self.save_user,
            fg_color=GUIConfig.COLORS["success"]
        )
        self.save_btn.pack(side="left", padx=5)
        
        self.delete_btn = ctk.CTkButton(
            self.buttons_frame,
            text="🗑️ 删除",
            command=self.delete_user,
            fg_color=GUIConfig.COLORS["danger"]
        )
        self.delete_btn.pack(side="left", padx=5)
        
        self.reset_password_btn = ctk.CTkButton(
            self.buttons_frame,
            text="🔑 重置密码",
            command=self.reset_password,
            fg_color=GUIConfig.COLORS["warning"]
        )
        self.reset_password_btn.pack(side="left", padx=5)
        
        # 权限设置按钮
        self.permissions_btn = ctk.CTkButton(
            self.buttons_frame,
            text="🔒 权限设置",
            command=self.show_permissions
        )
        self.permissions_btn.pack(side="left", padx=5)
        
        # 初始状态禁用按钮
        self.set_form_state(False)
        
    def create_context_menu(self):
        """创建右键菜单"""
        # 这里可以添加右键菜单功能
        pass
        
    def load_users(self):
        """加载用户列表"""
        # 清空现有数据
        for item in self.user_tree.get_children():
            self.user_tree.delete(item)
            
        # 模拟用户数据（实际项目中从数据库获取）
        users = [
            ("admin", "系统管理员", "<EMAIL>", "管理员", "active", "2024-01-07 10:30:00"),
            ("user1", "张三", "<EMAIL>", "普通用户", "active", "2024-01-07 09:15:00"),
            ("user2", "李四", "<EMAIL>", "高级用户", "active", "2024-01-06 16:45:00"),
            ("user3", "王五", "<EMAIL>", "只读用户", "disabled", "2024-01-05 14:20:00"),
        ]
        
        for user in users:
            self.user_tree.insert("", "end", values=user)
            
    def search_users(self):
        """搜索用户"""
        search_text = self.search_entry.get().strip()
        if not search_text:
            self.load_users()
            return
            
        # 这里实现搜索逻辑
        messagebox.showinfo("搜索", f"搜索功能开发中，搜索关键词: {search_text}")
        
    def on_user_select(self, event):
        """用户选择事件"""
        selection = self.user_tree.selection()
        if selection:
            item = self.user_tree.item(selection[0])
            values = item['values']
            
            # 填充表单
            self.username_entry.delete(0, "end")
            self.username_entry.insert(0, values[0])
            
            self.realname_entry.delete(0, "end")
            self.realname_entry.insert(0, values[1])
            
            self.email_entry.delete(0, "end")
            self.email_entry.insert(0, values[2])
            
            self.group_combo.set(values[3])
            self.status_combo.set(values[4])
            
            self.selected_user = values[0]
            self.set_form_state(True)
            
            # 隐藏密码字段
            self.password_label.pack_forget()
            self.password_entry.pack_forget()
            
    def add_user(self):
        """添加用户"""
        # 清空表单
        self.clear_form()
        
        # 显示密码字段
        self.password_label.pack(anchor="w", pady=(10, 0))
        self.password_entry.pack(fill="x", pady=(0, 10))
        
        self.selected_user = None
        self.set_form_state(True)
        
    def save_user(self):
        """保存用户"""
        username = self.username_entry.get().strip()
        if not username:
            messagebox.showerror("错误", "用户名不能为空")
            return
            
        # 这里实现保存逻辑
        if self.selected_user:
            messagebox.showinfo("成功", f"用户 {username} 信息已更新")
        else:
            password = self.password_entry.get().strip()
            if not password:
                messagebox.showerror("错误", "密码不能为空")
                return
            messagebox.showinfo("成功", f"用户 {username} 已创建")
            
        self.load_users()
        self.clear_form()
        self.set_form_state(False)
        
    def delete_user(self):
        """删除用户"""
        if not self.selected_user:
            return
            
        if messagebox.askyesno("确认", f"确定要删除用户 {self.selected_user} 吗？"):
            # 这里实现删除逻辑
            messagebox.showinfo("成功", f"用户 {self.selected_user} 已删除")
            self.load_users()
            self.clear_form()
            self.set_form_state(False)
            
    def reset_password(self):
        """重置密码"""
        if not self.selected_user:
            return
            
        # 这里可以打开密码重置对话框
        messagebox.showinfo("重置密码", f"用户 {self.selected_user} 的密码重置功能开发中")
        
    def show_permissions(self):
        """显示权限设置"""
        if not self.selected_user:
            return
            
        # 这里可以打开权限设置对话框
        messagebox.showinfo("权限设置", f"用户 {self.selected_user} 的权限设置功能开发中")
        
    def clear_form(self):
        """清空表单"""
        self.username_entry.delete(0, "end")
        self.realname_entry.delete(0, "end")
        self.email_entry.delete(0, "end")
        self.password_entry.delete(0, "end")
        self.group_combo.set("")
        self.status_combo.set("")
        
    def set_form_state(self, enabled):
        """设置表单状态"""
        state = "normal" if enabled else "disabled"
        
        self.save_btn.configure(state=state)
        self.delete_btn.configure(state=state)
        self.reset_password_btn.configure(state=state)
        self.permissions_btn.configure(state=state)
        
    def show(self):
        """显示页面"""
        self.frame.pack(fill="both", expand=True)
        self.is_visible = True
        
    def hide(self):
        """隐藏页面"""
        self.frame.pack_forget()
        self.is_visible = False
