"""
系统设置页面
"""
import customtkinter as ctk
from tkinter import messagebox
from server.config.config import GUIConfig, Config

class SettingsPage:
    """系统设置页面类"""
    
    def __init__(self, parent, main_window):
        self.parent = parent
        self.main_window = main_window
        self.is_visible = False
        
        self.create_widgets()
        self.load_settings()
        
    def create_widgets(self):
        """创建页面组件"""
        # 创建主框架
        self.frame = ctk.CTkScrollableFrame(self.parent)
        
        # 页面标题
        self.title_label = ctk.CTkLabel(
            self.frame,
            text="⚙️ 系统设置",
            font=self.main_window.title_font,
            anchor="w"
        )
        self.title_label.pack(fill="x", padx=20, pady=(20, 10))
        
        # 创建设置分组
        self.create_general_settings()
        self.create_security_settings()
        self.create_file_settings()
        self.create_network_settings()
        
        # 保存按钮
        self.save_btn = ctk.CTkButton(
            self.frame,
            text="💾 保存设置",
            command=self.save_settings,
            fg_color=GUIConfig.COLORS["success"],
            height=40
        )
        self.save_btn.pack(pady=20)
        
    def create_general_settings(self):
        """创建常规设置"""
        # 常规设置框架
        self.general_frame = ctk.CTkFrame(self.frame)
        self.general_frame.pack(fill="x", padx=20, pady=10)
        
        general_title = ctk.CTkLabel(
            self.general_frame,
            text="🔧 常规设置",
            font=self.main_window.title_font
        )
        general_title.pack(pady=10)
        
        # 系统名称
        self.system_name_label = ctk.CTkLabel(self.general_frame, text="系统名称:")
        self.system_name_label.pack(anchor="w", padx=20, pady=(10, 0))
        self.system_name_entry = ctk.CTkEntry(self.general_frame, width=300)
        self.system_name_entry.pack(anchor="w", padx=20, pady=(0, 10))
        
        # 启用用户注册
        self.enable_registration_var = ctk.BooleanVar()
        self.enable_registration_cb = ctk.CTkCheckBox(
            self.general_frame,
            text="启用用户注册",
            variable=self.enable_registration_var
        )
        self.enable_registration_cb.pack(anchor="w", padx=20, pady=5)
        
        # 启用通知功能
        self.enable_notifications_var = ctk.BooleanVar()
        self.enable_notifications_cb = ctk.CTkCheckBox(
            self.general_frame,
            text="启用通知功能",
            variable=self.enable_notifications_var
        )
        self.enable_notifications_cb.pack(anchor="w", padx=20, pady=5)
        
    def create_security_settings(self):
        """创建安全设置"""
        # 安全设置框架
        self.security_frame = ctk.CTkFrame(self.frame)
        self.security_frame.pack(fill="x", padx=20, pady=10)
        
        security_title = ctk.CTkLabel(
            self.security_frame,
            text="🔒 安全设置",
            font=self.main_window.title_font
        )
        security_title.pack(pady=10)
        
        # 密码最小长度
        self.password_min_label = ctk.CTkLabel(self.security_frame, text="密码最小长度:")
        self.password_min_label.pack(anchor="w", padx=20, pady=(10, 0))
        self.password_min_entry = ctk.CTkEntry(self.security_frame, width=100)
        self.password_min_entry.pack(anchor="w", padx=20, pady=(0, 10))
        
        # 会话超时时间
        self.session_timeout_label = ctk.CTkLabel(self.security_frame, text="会话超时时间(秒):")
        self.session_timeout_label.pack(anchor="w", padx=20, pady=(10, 0))
        self.session_timeout_entry = ctk.CTkEntry(self.security_frame, width=100)
        self.session_timeout_entry.pack(anchor="w", padx=20, pady=(0, 10))
        
        # 启用文件加密
        self.enable_encryption_var = ctk.BooleanVar()
        self.enable_encryption_cb = ctk.CTkCheckBox(
            self.security_frame,
            text="启用文件加密下载",
            variable=self.enable_encryption_var
        )
        self.enable_encryption_cb.pack(anchor="w", padx=20, pady=5)
        
        # 加密阈值
        self.encryption_threshold_label = ctk.CTkLabel(self.security_frame, text="加密阈值(下载次数):")
        self.encryption_threshold_label.pack(anchor="w", padx=20, pady=(10, 0))
        self.encryption_threshold_entry = ctk.CTkEntry(self.security_frame, width=100)
        self.encryption_threshold_entry.pack(anchor="w", padx=20, pady=(0, 10))
        
    def create_file_settings(self):
        """创建文件设置"""
        # 文件设置框架
        self.file_frame = ctk.CTkFrame(self.frame)
        self.file_frame.pack(fill="x", padx=20, pady=10)
        
        file_title = ctk.CTkLabel(
            self.file_frame,
            text="📁 文件设置",
            font=self.main_window.title_font
        )
        file_title.pack(pady=10)
        
        # 单次下载最大大小
        self.max_download_label = ctk.CTkLabel(self.file_frame, text="单次下载最大大小(MB):")
        self.max_download_label.pack(anchor="w", padx=20, pady=(10, 0))
        self.max_download_entry = ctk.CTkEntry(self.file_frame, width=100)
        self.max_download_entry.pack(anchor="w", padx=20, pady=(0, 10))
        
        # 批量下载最大文件数
        self.max_batch_label = ctk.CTkLabel(self.file_frame, text="批量下载最大文件数:")
        self.max_batch_label.pack(anchor="w", padx=20, pady=(10, 0))
        self.max_batch_entry = ctk.CTkEntry(self.file_frame, width=100)
        self.max_batch_entry.pack(anchor="w", padx=20, pady=(0, 10))
        
        # 缩略图质量
        self.thumbnail_quality_label = ctk.CTkLabel(self.file_frame, text="缩略图质量(1-100):")
        self.thumbnail_quality_label.pack(anchor="w", padx=20, pady=(10, 0))
        self.thumbnail_quality_entry = ctk.CTkEntry(self.file_frame, width=100)
        self.thumbnail_quality_entry.pack(anchor="w", padx=20, pady=(0, 10))
        
    def create_network_settings(self):
        """创建网络设置"""
        # 网络设置框架
        self.network_frame = ctk.CTkFrame(self.frame)
        self.network_frame.pack(fill="x", padx=20, pady=10)
        
        network_title = ctk.CTkLabel(
            self.network_frame,
            text="🌐 网络设置",
            font=self.main_window.title_font
        )
        network_title.pack(pady=10)
        
        # 服务器端口
        self.server_port_label = ctk.CTkLabel(self.network_frame, text="服务器端口:")
        self.server_port_label.pack(anchor="w", padx=20, pady=(10, 0))
        self.server_port_entry = ctk.CTkEntry(self.network_frame, width=100)
        self.server_port_entry.pack(anchor="w", padx=20, pady=(0, 10))
        
        # 启用外网访问
        self.enable_external_var = ctk.BooleanVar()
        self.enable_external_cb = ctk.CTkCheckBox(
            self.network_frame,
            text="启用外网访问",
            variable=self.enable_external_var
        )
        self.enable_external_cb.pack(anchor="w", padx=20, pady=5)
        
        # 搜索引擎类型
        self.search_engine_label = ctk.CTkLabel(self.network_frame, text="搜索引擎类型:")
        self.search_engine_label.pack(anchor="w", padx=20, pady=(10, 0))
        self.search_engine_combo = ctk.CTkComboBox(
            self.network_frame,
            values=["文件名搜索", "图像搜索", "双引擎"],
            width=200
        )
        self.search_engine_combo.pack(anchor="w", padx=20, pady=(0, 10))
        
    def load_settings(self):
        """加载设置"""
        # 这里从配置文件或数据库加载设置
        self.system_name_entry.insert(0, Config.APP_NAME)
        self.password_min_entry.insert(0, str(Config.PASSWORD_MIN_LENGTH))
        self.session_timeout_entry.insert(0, str(Config.JWT_ACCESS_TOKEN_EXPIRES))
        self.max_download_entry.insert(0, str(Config.MAX_DOWNLOAD_SIZE // (1024*1024)))
        self.max_batch_entry.insert(0, str(Config.MAX_BATCH_FILES))
        self.thumbnail_quality_entry.insert(0, str(Config.THUMBNAIL_QUALITY))
        self.server_port_entry.insert(0, str(Config.SERVER_PORT))
        
        self.enable_registration_var.set(False)
        self.enable_notifications_var.set(Config.ENABLE_NOTIFICATIONS)
        self.enable_encryption_var.set(Config.ENABLE_FILE_ENCRYPTION)
        self.enable_external_var.set(Config.ENABLE_EXTERNAL_ACCESS)
        self.encryption_threshold_entry.insert(0, str(Config.ENCRYPTION_THRESHOLD))
        
        if Config.ENABLE_FILENAME_SEARCH and Config.ENABLE_IMAGE_SEARCH:
            self.search_engine_combo.set("双引擎")
        elif Config.ENABLE_FILENAME_SEARCH:
            self.search_engine_combo.set("文件名搜索")
        else:
            self.search_engine_combo.set("图像搜索")
        
    def save_settings(self):
        """保存设置"""
        try:
            # 验证输入
            password_min = int(self.password_min_entry.get())
            session_timeout = int(self.session_timeout_entry.get())
            max_download = int(self.max_download_entry.get())
            max_batch = int(self.max_batch_entry.get())
            thumbnail_quality = int(self.thumbnail_quality_entry.get())
            server_port = int(self.server_port_entry.get())
            encryption_threshold = int(self.encryption_threshold_entry.get())
            
            if not (1 <= thumbnail_quality <= 100):
                raise ValueError("缩略图质量必须在1-100之间")
                
            if not (1024 <= server_port <= 65535):
                raise ValueError("端口号必须在1024-65535之间")
                
            # 这里实现保存逻辑
            messagebox.showinfo("成功", "设置已保存")
            
        except ValueError as e:
            messagebox.showerror("错误", f"输入验证失败: {str(e)}")
        except Exception as e:
            messagebox.showerror("错误", f"保存设置失败: {str(e)}")
            
    def show(self):
        """显示页面"""
        self.frame.pack(fill="both", expand=True)
        self.is_visible = True
        
    def hide(self):
        """隐藏页面"""
        self.frame.pack_forget()
        self.is_visible = False
