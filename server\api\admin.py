"""
管理员相关API
"""
from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from datetime import datetime, timedelta

from server.database.models import (
    db, User, UserGroup, SharedDirectory, FileIndex, 
    SystemLog, DownloadLog, SearchLog, OnlineUser, SystemConfig
)
from server.core.utils import get_client_ip, log_user_activity

admin_bp = Blueprint('admin', __name__)

def require_admin():
    """装饰器：要求管理员权限"""
    def decorator(f):
        def wrapper(*args, **kwargs):
            user_id = get_jwt_identity()
            user = User.query.get(user_id)
            if not user or not user.is_admin():
                return jsonify({
                    'success': False,
                    'message': '需要管理员权限'
                }), 403
            return f(*args, **kwargs)
        wrapper.__name__ = f.__name__
        return wrapper
    return decorator

@admin_bp.route('/dashboard/stats', methods=['GET'])
@jwt_required()
@require_admin()
def get_dashboard_stats():
    """获取仪表板统计数据"""
    try:
        # 在线用户数
        online_users = OnlineUser.query.count()
        
        # 总用户数
        total_users = User.query.count()
        
        # 文件总数
        total_files = FileIndex.query.count()
        
        # 今日下载次数
        today = datetime.utcnow().date()
        today_downloads = DownloadLog.query.filter(
            DownloadLog.download_time >= today
        ).count()
        
        # 今日搜索次数
        today_searches = SearchLog.query.filter(
            SearchLog.search_time >= today
        ).count()
        
        # 存储使用情况
        total_size = db.session.query(
            db.func.sum(FileIndex.file_size)
        ).scalar() or 0
        
        return jsonify({
            'success': True,
            'data': {
                'online_users': online_users,
                'total_users': total_users,
                'total_files': total_files,
                'today_downloads': today_downloads,
                'today_searches': today_searches,
                'total_size': total_size
            }
        })
        
    except Exception as e:
        current_app.logger.error(f"获取仪表板统计错误: {e}")
        return jsonify({
            'success': False,
            'message': '获取统计数据失败'
        }), 500

@admin_bp.route('/users', methods=['GET'])
@jwt_required()
@require_admin()
def list_users():
    """获取用户列表"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        search = request.args.get('search', '').strip()
        
        query = User.query
        
        if search:
            query = query.filter(
                db.or_(
                    User.username.contains(search),
                    User.real_name.contains(search),
                    User.email.contains(search)
                )
            )
        
        pagination = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )
        
        users = []
        for user in pagination.items:
            users.append({
                'id': user.id,
                'username': user.username,
                'real_name': user.real_name,
                'email': user.email,
                'status': user.status,
                'group': user.group.group_name if user.group else None,
                'last_login_time': user.last_login_time.isoformat() if user.last_login_time else None,
                'login_count': user.login_count,
                'created_at': user.created_at.isoformat()
            })
        
        return jsonify({
            'success': True,
            'data': {
                'users': users,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': pagination.total,
                    'pages': pagination.pages
                }
            }
        })
        
    except Exception as e:
        current_app.logger.error(f"获取用户列表错误: {e}")
        return jsonify({
            'success': False,
            'message': '获取用户列表失败'
        }), 500

@admin_bp.route('/users/<int:user_id>', methods=['PUT'])
@jwt_required()
@require_admin()
def update_user(user_id):
    """更新用户信息"""
    try:
        data = request.get_json()
        user = User.query.get_or_404(user_id)
        
        # 更新用户信息
        if 'real_name' in data:
            user.real_name = data['real_name']
        
        if 'email' in data:
            user.email = data['email']
        
        if 'status' in data:
            user.status = data['status']
        
        if 'user_group_id' in data:
            user.user_group_id = data['user_group_id']
        
        db.session.commit()
        
        log_user_activity(
            get_jwt_identity(),
            'USER_UPDATE',
            f'管理员更新用户 {user.username} 信息'
        )
        
        return jsonify({
            'success': True,
            'message': '用户信息更新成功'
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"更新用户信息错误: {e}")
        return jsonify({
            'success': False,
            'message': '更新用户信息失败'
        }), 500

@admin_bp.route('/logs', methods=['GET'])
@jwt_required()
@require_admin()
def get_system_logs():
    """获取系统日志"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 50, type=int)
        log_level = request.args.get('level', '')
        log_type = request.args.get('type', '')
        
        query = SystemLog.query
        
        if log_level:
            query = query.filter_by(log_level=log_level)
        
        if log_type:
            query = query.filter_by(log_type=log_type)
        
        query = query.order_by(SystemLog.created_at.desc())
        
        pagination = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )
        
        logs = []
        for log in pagination.items:
            logs.append({
                'id': log.id,
                'level': log.log_level,
                'type': log.log_type,
                'message': log.message,
                'user': log.user.username if log.user else None,
                'client_ip': log.client_ip,
                'created_at': log.created_at.isoformat()
            })
        
        return jsonify({
            'success': True,
            'data': {
                'logs': logs,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': pagination.total,
                    'pages': pagination.pages
                }
            }
        })
        
    except Exception as e:
        current_app.logger.error(f"获取系统日志错误: {e}")
        return jsonify({
            'success': False,
            'message': '获取系统日志失败'
        }), 500

@admin_bp.route('/config', methods=['GET'])
@jwt_required()
@require_admin()
def get_system_config():
    """获取系统配置"""
    try:
        configs = SystemConfig.query.all()
        
        result = {}
        for config in configs:
            result[config.config_key] = {
                'value': config.config_value,
                'description': config.description
            }
        
        return jsonify({
            'success': True,
            'data': result
        })
        
    except Exception as e:
        current_app.logger.error(f"获取系统配置错误: {e}")
        return jsonify({
            'success': False,
            'message': '获取系统配置失败'
        }), 500

@admin_bp.route('/config', methods=['PUT'])
@jwt_required()
@require_admin()
def update_system_config():
    """更新系统配置"""
    try:
        data = request.get_json()
        
        for key, value in data.items():
            config = SystemConfig.query.filter_by(config_key=key).first()
            if config:
                config.config_value = str(value)
                config.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        log_user_activity(
            get_jwt_identity(),
            'CONFIG_UPDATE',
            '管理员更新系统配置'
        )
        
        return jsonify({
            'success': True,
            'message': '系统配置更新成功'
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"更新系统配置错误: {e}")
        return jsonify({
            'success': False,
            'message': '更新系统配置失败'
        }), 500
