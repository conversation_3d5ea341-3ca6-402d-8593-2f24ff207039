"""
Flask API应用主文件
"""
from flask import Flask, jsonify, request
from flask_sqlalchemy import SQLAlchemy
from flask_jwt_extended import JWTManager
from flask_cors import CORS
import logging
from logging.handlers import RotatingFileHandler
import os
import sys

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from server.config.config import config, LOGGING_CONFIG
from server.database.models import db
from server.api.auth import auth_bp
from server.api.files import files_bp
from server.api.users import users_bp
from server.api.search import search_bp
from server.api.admin import admin_bp

def create_app(config_name='development'):
    """创建Flask应用"""
    app = Flask(__name__)
    
    # 加载配置
    app.config.from_object(config[config_name])
    config[config_name].init_app(app)
    
    # 初始化扩展
    db.init_app(app)
    jwt = JWTManager(app)
    CORS(app)
    
    # 配置日志
    setup_logging(app)
    
    # 注册蓝图
    register_blueprints(app)
    
    # 注册错误处理器
    register_error_handlers(app)
    
    # 创建数据库表
    with app.app_context():
        try:
            db.create_all()
            app.logger.info("数据库表创建成功")
        except Exception as e:
            app.logger.error(f"数据库表创建失败: {e}")
    
    return app

def setup_logging(app):
    """设置日志"""
    if not app.debug and not app.testing:
        # 配置日志处理器
        if not os.path.exists('logs'):
            os.mkdir('logs')
            
        file_handler = RotatingFileHandler(
            'logs/app.log',
            maxBytes=10240000,
            backupCount=10
        )
        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
        ))
        file_handler.setLevel(logging.INFO)
        app.logger.addHandler(file_handler)
        
        app.logger.setLevel(logging.INFO)
        app.logger.info('文件共享系统启动')

def register_blueprints(app):
    """注册蓝图"""
    # API路由前缀
    api_prefix = '/api/v1'
    
    # 认证相关
    app.register_blueprint(auth_bp, url_prefix=f'{api_prefix}/auth')
    
    # 文件相关
    app.register_blueprint(files_bp, url_prefix=f'{api_prefix}/files')
    
    # 用户相关
    app.register_blueprint(users_bp, url_prefix=f'{api_prefix}/users')
    
    # 搜索相关
    app.register_blueprint(search_bp, url_prefix=f'{api_prefix}/search')
    
    # 管理员相关
    app.register_blueprint(admin_bp, url_prefix=f'{api_prefix}/admin')
    
    # 根路由
    @app.route('/')
    def index():
        return jsonify({
            'message': '企业文件共享系统 API',
            'version': '1.0.0',
            'status': 'running'
        })
    
    # 健康检查
    @app.route('/health')
    def health_check():
        return jsonify({
            'status': 'healthy',
            'timestamp': datetime.utcnow().isoformat()
        })

def register_error_handlers(app):
    """注册错误处理器"""
    
    @app.errorhandler(400)
    def bad_request(error):
        return jsonify({
            'error': 'Bad Request',
            'message': '请求参数错误'
        }), 400
    
    @app.errorhandler(401)
    def unauthorized(error):
        return jsonify({
            'error': 'Unauthorized',
            'message': '未授权访问'
        }), 401
    
    @app.errorhandler(403)
    def forbidden(error):
        return jsonify({
            'error': 'Forbidden',
            'message': '权限不足'
        }), 403
    
    @app.errorhandler(404)
    def not_found(error):
        return jsonify({
            'error': 'Not Found',
            'message': '资源不存在'
        }), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        db.session.rollback()
        return jsonify({
            'error': 'Internal Server Error',
            'message': '服务器内部错误'
        }), 500

# JWT配置
@jwt.expired_token_loader
def expired_token_callback(jwt_header, jwt_payload):
    return jsonify({
        'error': 'Token Expired',
        'message': '令牌已过期'
    }), 401

@jwt.invalid_token_loader
def invalid_token_callback(error):
    return jsonify({
        'error': 'Invalid Token',
        'message': '无效令牌'
    }), 401

@jwt.unauthorized_loader
def missing_token_callback(error):
    return jsonify({
        'error': 'Missing Token',
        'message': '缺少访问令牌'
    }), 401

if __name__ == '__main__':
    from datetime import datetime
    app = create_app()
    app.run(host='0.0.0.0', port=5000, debug=True)
