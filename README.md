# 企业级文件共享系统

## 项目概述
这是一个企业级文件共享系统，支持内外网访问控制、双搜索引擎、实时监控、缩略图支持、加密下载等功能。

## 技术栈
- **后端**: Python + Flask + MySQL
- **前端**: HTML + CSS + JavaScript
- **服务端界面**: Python + CustomTkinter
- **搜索引擎**: Everything-like + OpenCV图像识别
- **部署**: Windows原生部署（无Docker）

## 项目结构
```
Net/
├── server/                 # 服务端程序
│   ├── gui/               # WinForm界面
│   ├── api/               # API接口
│   ├── core/              # 核心功能
│   ├── database/          # 数据库相关
│   └── config/            # 配置文件
├── client/                # 客户端程序
├── web/                   # Web前端
│   ├── admin/             # 管理员界面
│   └── user/              # 用户界面
├── shared/                # 共享模块
├── docs/                  # 文档
└── tests/                 # 测试文件
```

## 核心功能
1. **权限管理**: 内网/外网访问控制，文件夹级别权限
2. **双搜索引擎**: 文件名快速搜索 + 图像识别搜索
3. **文件管理**: 缩略图、批量下载、加密压缩
4. **用户管理**: 注册机制、用户组、权限控制
5. **监控日志**: 用户行为记录、实时监控、统计分析
6. **安全机制**: 加密下载、密码保护、访问限制

## 安装部署
1. 安装Python 3.8+
2. 安装MySQL 5.7+
3. 安装依赖: `pip install -r requirements.txt`
4. 配置数据库连接
5. 运行服务端程序

## 开发状态
- [x] 项目结构设计
- [ ] 数据库设计
- [ ] 服务端WinForm界面
- [ ] API接口开发
- [ ] 搜索引擎实现
- [ ] 前端界面开发
- [ ] 测试与部署
