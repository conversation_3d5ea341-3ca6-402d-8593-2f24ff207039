"""
日志管理页面
"""
import customtkinter as ctk
from tkinter import ttk, messagebox
from server.config.config import GUIConfig

class LogsPage:
    """日志管理页面类"""
    
    def __init__(self, parent, main_window):
        self.parent = parent
        self.main_window = main_window
        self.is_visible = False
        
        self.create_widgets()
        self.load_logs()
        
    def create_widgets(self):
        """创建页面组件"""
        # 创建主框架
        self.frame = ctk.CTkFrame(self.parent)
        
        # 页面标题
        self.title_label = ctk.CTkLabel(
            self.frame,
            text="📋 日志管理",
            font=self.main_window.title_font,
            anchor="w"
        )
        self.title_label.pack(fill="x", padx=20, pady=(20, 10))
        
        # 创建工具栏
        self.create_toolbar()
        
        # 创建日志列表
        self.create_log_list()
        
    def create_toolbar(self):
        """创建工具栏"""
        self.toolbar_frame = ctk.CTkFrame(self.frame)
        self.toolbar_frame.pack(fill="x", padx=20, pady=10)
        
        # 日志类型筛选
        self.log_type_label = ctk.CTkLabel(self.toolbar_frame, text="日志类型:")
        self.log_type_label.pack(side="left", padx=10, pady=10)
        
        self.log_type_combo = ctk.CTkComboBox(
            self.toolbar_frame,
            values=["全部", "INFO", "WARNING", "ERROR", "CRITICAL"],
            width=120,
            command=self.filter_logs
        )
        self.log_type_combo.pack(side="left", padx=10, pady=10)
        
        # 搜索框
        self.search_entry = ctk.CTkEntry(
            self.toolbar_frame,
            placeholder_text="搜索日志内容...",
            width=200
        )
        self.search_entry.pack(side="left", padx=10, pady=10)
        
        self.search_btn = ctk.CTkButton(
            self.toolbar_frame,
            text="🔍 搜索",
            width=80,
            command=self.search_logs
        )
        self.search_btn.pack(side="left", padx=5, pady=10)
        
        # 操作按钮
        self.refresh_btn = ctk.CTkButton(
            self.toolbar_frame,
            text="🔄 刷新",
            width=80,
            command=self.load_logs
        )
        self.refresh_btn.pack(side="right", padx=5, pady=10)
        
        self.export_btn = ctk.CTkButton(
            self.toolbar_frame,
            text="📤 导出",
            width=80,
            command=self.export_logs
        )
        self.export_btn.pack(side="right", padx=5, pady=10)
        
        self.clear_btn = ctk.CTkButton(
            self.toolbar_frame,
            text="🗑️ 清空",
            width=80,
            command=self.clear_logs,
            fg_color=GUIConfig.COLORS["danger"]
        )
        self.clear_btn.pack(side="right", padx=5, pady=10)
        
    def create_log_list(self):
        """创建日志列表"""
        # 日志列表框架
        self.list_frame = ctk.CTkFrame(self.frame)
        self.list_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # 日志表格
        columns = ("时间", "级别", "类型", "用户", "消息", "IP地址")
        self.log_tree = ttk.Treeview(self.list_frame, columns=columns, show="headings", height=20)
        
        # 设置列标题和宽度
        column_widths = {"时间": 150, "级别": 80, "类型": 100, "用户": 100, "消息": 300, "IP地址": 120}
        for col in columns:
            self.log_tree.heading(col, text=col)
            self.log_tree.column(col, width=column_widths.get(col, 100))
            
        # 滚动条
        scrollbar_y = ttk.Scrollbar(self.list_frame, orient="vertical", command=self.log_tree.yview)
        scrollbar_x = ttk.Scrollbar(self.list_frame, orient="horizontal", command=self.log_tree.xview)
        self.log_tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)
        
        # 布局
        self.log_tree.pack(side="left", fill="both", expand=True)
        scrollbar_y.pack(side="right", fill="y")
        scrollbar_x.pack(side="bottom", fill="x")
        
        # 绑定双击事件
        self.log_tree.bind("<Double-1>", self.show_log_detail)
        
    def load_logs(self):
        """加载日志"""
        # 清空现有数据
        for item in self.log_tree.get_children():
            self.log_tree.delete(item)
            
        # 模拟日志数据
        logs = [
            ("2024-01-07 10:30:15", "INFO", "用户登录", "admin", "管理员登录系统", "192.168.1.100"),
            ("2024-01-07 10:25:32", "INFO", "文件下载", "user1", "下载文件: image001.jpg", "*************"),
            ("2024-01-07 10:20:45", "WARNING", "权限检查", "user2", "尝试访问受限文件", "*************"),
            ("2024-01-07 10:15:18", "ERROR", "文件操作", "user3", "文件上传失败: 磁盘空间不足", "*************"),
            ("2024-01-07 10:10:22", "INFO", "搜索操作", "user1", "搜索关键词: 设计图", "*************"),
            ("2024-01-07 10:05:33", "CRITICAL", "系统错误", "system", "数据库连接失败", "localhost"),
            ("2024-01-07 10:00:11", "INFO", "系统启动", "system", "文件共享系统启动", "localhost"),
        ]
        
        for log in logs:
            # 根据日志级别设置颜色
            level = log[1]
            if level == "ERROR" or level == "CRITICAL":
                tags = ("error",)
            elif level == "WARNING":
                tags = ("warning",)
            else:
                tags = ("info",)
                
            self.log_tree.insert("", "end", values=log, tags=tags)
            
        # 设置标签颜色
        self.log_tree.tag_configure("error", foreground="red")
        self.log_tree.tag_configure("warning", foreground="orange")
        self.log_tree.tag_configure("info", foreground="black")
        
    def filter_logs(self, log_type):
        """筛选日志"""
        # 这里实现日志筛选逻辑
        messagebox.showinfo("筛选", f"筛选日志类型: {log_type}")
        
    def search_logs(self):
        """搜索日志"""
        search_text = self.search_entry.get().strip()
        if not search_text:
            self.load_logs()
            return
            
        # 这里实现搜索逻辑
        messagebox.showinfo("搜索", f"搜索日志内容: {search_text}")
        
    def show_log_detail(self, event):
        """显示日志详情"""
        selection = self.log_tree.selection()
        if selection:
            item = self.log_tree.item(selection[0])
            values = item['values']
            
            # 创建详情窗口
            detail_window = ctk.CTkToplevel(self.frame)
            detail_window.title("日志详情")
            detail_window.geometry("600x400")
            
            # 详情内容
            detail_text = f"""
时间: {values[0]}
级别: {values[1]}
类型: {values[2]}
用户: {values[3]}
消息: {values[4]}
IP地址: {values[5]}

详细信息:
这里可以显示更详细的日志信息，包括堆栈跟踪、
请求参数、响应数据等。
            """
            
            detail_textbox = ctk.CTkTextbox(detail_window)
            detail_textbox.pack(fill="both", expand=True, padx=20, pady=20)
            detail_textbox.insert("1.0", detail_text)
            detail_textbox.configure(state="disabled")
            
    def export_logs(self):
        """导出日志"""
        self.main_window.export_logs()
        
    def clear_logs(self):
        """清空日志"""
        if messagebox.askyesno("确认", "确定要清空所有日志吗？此操作不可恢复！"):
            # 这里实现清空日志逻辑
            messagebox.showinfo("成功", "日志已清空")
            self.load_logs()
            
    def show(self):
        """显示页面"""
        self.frame.pack(fill="both", expand=True)
        self.is_visible = True
        
    def hide(self):
        """隐藏页面"""
        self.frame.pack_forget()
        self.is_visible = False
