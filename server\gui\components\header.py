"""
头部组件
"""
import customtkinter as ctk
from datetime import datetime
import threading
import time
from server.config.config import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Config

class Header:
    """头部组件类"""
    
    def __init__(self, parent, main_window):
        self.parent = parent
        self.main_window = main_window
        self.server_status = False
        self.notification_text = "欢迎使用企业文件共享系统"
        self.notification_position = 0
        
        self.create_widgets()
        self.start_clock()
        self.start_notification_scroll()
        
    def create_widgets(self):
        """创建头部组件"""
        # 创建头部框架
        self.header_frame = ctk.CTkFrame(
            self.parent,
            height=GUIConfig.HEADER_HEIGHT,
            corner_radius=10
        )
        self.header_frame.pack(fill="x", pady=(0, 5))
        self.header_frame.pack_propagate(False)
        
        # 左侧：系统标题和状态
        self.left_frame = ctk.CTkFrame(self.header_frame, fg_color="transparent")
        self.left_frame.pack(side="left", fill="y", padx=20)
        
        self.title_label = ctk.CTkLabel(
            self.left_frame,
            text=Config.APP_NAME,
            font=self.main_window.title_font,
            text_color=GUIConfig.COLORS["primary"]
        )
        self.title_label.pack(side="left", pady=15)
        
        # 服务器状态指示器
        self.status_frame = ctk.CTkFrame(self.left_frame, fg_color="transparent")
        self.status_frame.pack(side="left", padx=(20, 0), pady=15)
        
        self.status_indicator = ctk.CTkLabel(
            self.status_frame,
            text="●",
            font=ctk.CTkFont(size=20),
            text_color=GUIConfig.COLORS["danger"]
        )
        self.status_indicator.pack(side="left")
        
        self.status_text = ctk.CTkLabel(
            self.status_frame,
            text="服务器离线",
            font=self.main_window.default_font
        )
        self.status_text.pack(side="left", padx=(5, 0))
        
        # 中间：滚动通知
        self.center_frame = ctk.CTkFrame(self.header_frame)
        self.center_frame.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        
        self.notification_label = ctk.CTkLabel(
            self.center_frame,
            text="",
            font=self.main_window.default_font,
            anchor="w"
        )
        self.notification_label.pack(fill="both", expand=True, padx=10, pady=5)
        
        # 右侧：时间和操作按钮
        self.right_frame = ctk.CTkFrame(self.header_frame, fg_color="transparent")
        self.right_frame.pack(side="right", fill="y", padx=20)
        
        # 时间显示
        self.time_label = ctk.CTkLabel(
            self.right_frame,
            text="",
            font=self.main_window.default_font
        )
        self.time_label.pack(side="right", pady=15)
        
        # 操作按钮
        self.actions_frame = ctk.CTkFrame(self.right_frame, fg_color="transparent")
        self.actions_frame.pack(side="right", padx=(0, 20), pady=15)
        
        # 重启服务器按钮
        self.restart_btn = ctk.CTkButton(
            self.actions_frame,
            text="🔄",
            width=30,
            height=30,
            font=ctk.CTkFont(size=16),
            command=self.main_window.restart_server,
            corner_radius=15
        )
        self.restart_btn.pack(side="right", padx=2)
        
        # 设置按钮
        self.settings_btn = ctk.CTkButton(
            self.actions_frame,
            text="⚙️",
            width=30,
            height=30,
            font=ctk.CTkFont(size=16),
            command=self.main_window.show_settings,
            corner_radius=15
        )
        self.settings_btn.pack(side="right", padx=2)
        
        # 关于按钮
        self.about_btn = ctk.CTkButton(
            self.actions_frame,
            text="ℹ️",
            width=30,
            height=30,
            font=ctk.CTkFont(size=16),
            command=self.main_window.show_about,
            corner_radius=15
        )
        self.about_btn.pack(side="right", padx=2)
        
    def start_clock(self):
        """启动时钟更新"""
        def update_clock():
            while True:
                current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                self.time_label.configure(text=current_time)
                time.sleep(1)
                
        clock_thread = threading.Thread(target=update_clock, daemon=True)
        clock_thread.start()
        
    def start_notification_scroll(self):
        """启动通知滚动"""
        def scroll_notification():
            while True:
                if len(self.notification_text) > 50:
                    # 滚动长文本
                    display_text = self.notification_text[self.notification_position:self.notification_position + 50]
                    if len(display_text) < 50:
                        display_text += " " * (50 - len(display_text))
                        display_text += self.notification_text[:50 - len(self.notification_text[self.notification_position:])]
                    
                    self.notification_label.configure(text=display_text)
                    
                    self.notification_position += 1
                    if self.notification_position >= len(self.notification_text):
                        self.notification_position = 0
                else:
                    # 短文本直接显示
                    self.notification_label.configure(text=self.notification_text)
                
                time.sleep(0.1)
                
        scroll_thread = threading.Thread(target=scroll_notification, daemon=True)
        scroll_thread.start()
        
    def update_server_status(self, is_running):
        """更新服务器状态"""
        self.server_status = is_running
        
        if is_running:
            self.status_indicator.configure(text_color=GUIConfig.COLORS["success"])
            self.status_text.configure(text="服务器在线")
            self.set_notification("服务器已启动，系统正常运行中...")
        else:
            self.status_indicator.configure(text_color=GUIConfig.COLORS["danger"])
            self.status_text.configure(text="服务器离线")
            self.set_notification("服务器已停止，请启动服务器以提供文件共享服务")
            
    def set_notification(self, text):
        """设置通知文本"""
        self.notification_text = text
        self.notification_position = 0
        
    def add_notification(self, text):
        """添加新通知（追加到现有通知后）"""
        current_time = datetime.now().strftime("%H:%M:%S")
        new_notification = f"[{current_time}] {text}"
        
        if self.notification_text:
            self.notification_text += f" | {new_notification}"
        else:
            self.notification_text = new_notification
            
    def show_system_info(self):
        """显示系统信息"""
        import psutil
        
        # 获取系统信息
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        info_text = f"CPU: {cpu_percent}% | 内存: {memory.percent}% | 磁盘: {disk.percent}%"
        self.set_notification(info_text)
        
    def show_user_count(self, online_count, total_count):
        """显示用户统计"""
        info_text = f"在线用户: {online_count} | 总用户数: {total_count}"
        self.add_notification(info_text)
        
    def show_file_stats(self, file_count, total_size):
        """显示文件统计"""
        size_mb = total_size / (1024 * 1024)
        info_text = f"共享文件: {file_count} 个 | 总大小: {size_mb:.1f} MB"
        self.add_notification(info_text)
        
    def show_download_stats(self, today_downloads, today_size):
        """显示下载统计"""
        size_mb = today_size / (1024 * 1024)
        info_text = f"今日下载: {today_downloads} 次 | 下载量: {size_mb:.1f} MB"
        self.add_notification(info_text)
