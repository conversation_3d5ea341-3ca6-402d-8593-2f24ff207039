-- 企业级文件共享系统数据库设计
-- 数据库: file_sharing_system
-- 字符集: utf8mb4 (支持中文)

CREATE DATABASE IF NOT EXISTS file_sharing_system 
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE file_sharing_system;

-- 系统配置表
CREATE TABLE system_config (
    id INT PRIMARY KEY AUTO_INCREMENT,
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    description VARCHAR(255) COMMENT '配置描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT '系统配置表';

-- 用户表
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    email VARCHAR(100) COMMENT '邮箱',
    real_name VARCHAR(100) COMMENT '真实姓名',
    user_group_id INT COMMENT '用户组ID',
    status ENUM('active', 'disabled', 'banned') DEFAULT 'active' COMMENT '用户状态',
    last_login_time TIMESTAMP NULL COMMENT '最后登录时间',
    last_login_ip VARCHAR(45) COMMENT '最后登录IP',
    login_count INT DEFAULT 0 COMMENT '登录次数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_user_group (user_group_id),
    INDEX idx_status (status)
) COMMENT '用户表';

-- 用户组表
CREATE TABLE user_groups (
    id INT PRIMARY KEY AUTO_INCREMENT,
    group_name VARCHAR(50) NOT NULL UNIQUE COMMENT '组名',
    description VARCHAR(255) COMMENT '组描述',
    permissions JSON COMMENT '权限配置',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT '用户组表';

-- 共享目录表
CREATE TABLE shared_directories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '目录名称',
    path VARCHAR(500) NOT NULL COMMENT '物理路径',
    description VARCHAR(255) COMMENT '目录描述',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    allow_internal BOOLEAN DEFAULT TRUE COMMENT '允许内网访问',
    allow_external BOOLEAN DEFAULT FALSE COMMENT '允许外网访问',
    default_permissions JSON COMMENT '默认权限',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_path (path(255)),
    INDEX idx_active (is_active)
) COMMENT '共享目录表';

-- 文件索引表
CREATE TABLE file_index (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    shared_dir_id INT NOT NULL COMMENT '共享目录ID',
    file_path VARCHAR(1000) NOT NULL COMMENT '文件相对路径',
    file_name VARCHAR(255) NOT NULL COMMENT '文件名',
    file_size BIGINT COMMENT '文件大小',
    file_type VARCHAR(50) COMMENT '文件类型',
    file_hash VARCHAR(64) COMMENT '文件哈希值',
    has_thumbnail BOOLEAN DEFAULT FALSE COMMENT '是否有缩略图',
    thumbnail_path VARCHAR(500) COMMENT '缩略图路径',
    image_features JSON COMMENT '图像特征数据',
    last_modified TIMESTAMP COMMENT '文件最后修改时间',
    indexed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '索引时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (shared_dir_id) REFERENCES shared_directories(id) ON DELETE CASCADE,
    INDEX idx_shared_dir (shared_dir_id),
    INDEX idx_file_name (file_name),
    INDEX idx_file_type (file_type),
    INDEX idx_file_hash (file_hash),
    INDEX idx_last_modified (last_modified),
    FULLTEXT idx_file_path (file_path, file_name)
) COMMENT '文件索引表';

-- 用户权限表
CREATE TABLE user_permissions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL COMMENT '用户ID',
    shared_dir_id INT NOT NULL COMMENT '共享目录ID',
    can_read BOOLEAN DEFAULT TRUE COMMENT '读取权限',
    can_write BOOLEAN DEFAULT FALSE COMMENT '写入权限',
    can_delete BOOLEAN DEFAULT FALSE COMMENT '删除权限',
    can_download BOOLEAN DEFAULT TRUE COMMENT '下载权限',
    can_upload BOOLEAN DEFAULT FALSE COMMENT '上传权限',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (shared_dir_id) REFERENCES shared_directories(id) ON DELETE CASCADE,
    UNIQUE KEY uk_user_dir (user_id, shared_dir_id)
) COMMENT '用户权限表';

-- 下载记录表
CREATE TABLE download_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL COMMENT '用户ID',
    file_id BIGINT COMMENT '文件ID',
    file_path VARCHAR(1000) COMMENT '文件路径',
    download_type ENUM('single', 'batch', 'folder') COMMENT '下载类型',
    file_count INT DEFAULT 1 COMMENT '文件数量',
    total_size BIGINT COMMENT '总大小',
    is_encrypted BOOLEAN DEFAULT FALSE COMMENT '是否加密',
    password_requested BOOLEAN DEFAULT FALSE COMMENT '是否申请密码',
    client_ip VARCHAR(45) COMMENT '客户端IP',
    user_agent TEXT COMMENT '用户代理',
    download_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '下载时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (file_id) REFERENCES file_index(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_download_time (download_time),
    INDEX idx_file_id (file_id)
) COMMENT '下载记录表';

-- 搜索记录表
CREATE TABLE search_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL COMMENT '用户ID',
    search_keyword VARCHAR(500) COMMENT '搜索关键词',
    search_type ENUM('filename', 'image', 'mixed') COMMENT '搜索类型',
    result_count INT DEFAULT 0 COMMENT '结果数量',
    search_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '搜索时间',
    client_ip VARCHAR(45) COMMENT '客户端IP',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_search_time (search_time),
    INDEX idx_search_type (search_type)
) COMMENT '搜索记录表';

-- 系统日志表
CREATE TABLE system_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    log_level ENUM('INFO', 'WARNING', 'ERROR', 'CRITICAL') DEFAULT 'INFO',
    log_type VARCHAR(50) COMMENT '日志类型',
    user_id INT COMMENT '相关用户ID',
    message TEXT COMMENT '日志消息',
    details JSON COMMENT '详细信息',
    client_ip VARCHAR(45) COMMENT '客户端IP',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_log_level (log_level),
    INDEX idx_log_type (log_type),
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at)
) COMMENT '系统日志表';

-- 注册码表
CREATE TABLE license_keys (
    id INT PRIMARY KEY AUTO_INCREMENT,
    license_key VARCHAR(100) NOT NULL UNIQUE COMMENT '注册码',
    max_users INT DEFAULT 1 COMMENT '最大用户数',
    used_users INT DEFAULT 0 COMMENT '已使用用户数',
    expire_date DATE COMMENT '过期日期',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否有效',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    used_at TIMESTAMP NULL COMMENT '使用时间',
    INDEX idx_license_key (license_key),
    INDEX idx_expire_date (expire_date)
) COMMENT '注册码表';

-- 在线用户表
CREATE TABLE online_users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL COMMENT '用户ID',
    session_id VARCHAR(100) NOT NULL COMMENT '会话ID',
    client_ip VARCHAR(45) COMMENT '客户端IP',
    user_agent TEXT COMMENT '用户代理',
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    login_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY uk_session (session_id),
    INDEX idx_user_id (user_id),
    INDEX idx_last_activity (last_activity)
) COMMENT '在线用户表';

-- 插入默认数据
INSERT INTO system_config (config_key, config_value, description) VALUES
('system_name', '企业文件共享系统', '系统名称'),
('max_download_size', '**********', '单次下载最大大小(字节)'),
('max_batch_files', '100', '批量下载最大文件数'),
('enable_external_access', 'false', '是否启用外网访问'),
('enable_registration', 'false', '是否启用用户注册'),
('thumbnail_quality', '80', '缩略图质量'),
('search_engine_type', 'both', '搜索引擎类型'),
('password_min_length', '6', '密码最小长度'),
('session_timeout', '3600', '会话超时时间(秒)'),
('enable_file_encryption', 'true', '是否启用文件加密');

INSERT INTO user_groups (group_name, description, permissions) VALUES
('管理员', '系统管理员组', '{"admin": true, "read": true, "write": true, "delete": true, "download": true, "upload": true}'),
('高级用户', '高级用户组', '{"admin": false, "read": true, "write": true, "delete": false, "download": true, "upload": true}'),
('普通用户', '普通用户组', '{"admin": false, "read": true, "write": false, "delete": false, "download": true, "upload": false}'),
('只读用户', '只读用户组', '{"admin": false, "read": true, "write": false, "delete": false, "download": false, "upload": false}');

-- 创建默认管理员用户 (密码: admin123)
INSERT INTO users (username, password_hash, real_name, user_group_id, status) VALUES
('admin', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS', '系统管理员', 1, 'active');
