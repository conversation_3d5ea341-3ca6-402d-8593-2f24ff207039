"""
底部状态栏组件
"""
import customtkinter as ctk
import threading
import time
import psutil
from server.config.config import GUIConfig

class Footer:
    """底部状态栏类"""
    
    def __init__(self, parent, main_window):
        self.parent = parent
        self.main_window = main_window
        self.status_text = "系统就绪"
        
        self.create_widgets()
        self.start_system_monitor()
        
    def create_widgets(self):
        """创建底部组件"""
        # 创建底部框架
        self.footer_frame = ctk.CTkFrame(
            self.parent,
            height=GUIConfig.FOOTER_HEIGHT,
            corner_radius=10
        )
        self.footer_frame.pack(fill="x", pady=(5, 0))
        self.footer_frame.pack_propagate(False)
        
        # 左侧：状态信息
        self.status_label = ctk.CTkLabel(
            self.footer_frame,
            text=self.status_text,
            font=self.main_window.default_font,
            anchor="w"
        )
        self.status_label.pack(side="left", padx=20, pady=5)
        
        # 右侧：系统监控信息
        self.monitor_frame = ctk.CTkFrame(self.footer_frame, fg_color="transparent")
        self.monitor_frame.pack(side="right", padx=20, pady=5)
        
        # CPU使用率
        self.cpu_label = ctk.CTkLabel(
            self.monitor_frame,
            text="CPU: 0%",
            font=ctk.CTkFont(size=10),
            width=60
        )
        self.cpu_label.pack(side="right", padx=5)
        
        # 内存使用率
        self.memory_label = ctk.CTkLabel(
            self.monitor_frame,
            text="内存: 0%",
            font=ctk.CTkFont(size=10),
            width=60
        )
        self.memory_label.pack(side="right", padx=5)
        
        # 网络状态
        self.network_label = ctk.CTkLabel(
            self.monitor_frame,
            text="网络: 正常",
            font=ctk.CTkFont(size=10),
            width=70
        )
        self.network_label.pack(side="right", padx=5)
        
        # 在线用户数
        self.users_label = ctk.CTkLabel(
            self.monitor_frame,
            text="在线: 0",
            font=ctk.CTkFont(size=10),
            width=50
        )
        self.users_label.pack(side="right", padx=5)
        
    def start_system_monitor(self):
        """启动系统监控"""
        def monitor_system():
            while True:
                try:
                    # 获取CPU使用率
                    cpu_percent = psutil.cpu_percent(interval=1)
                    self.cpu_label.configure(text=f"CPU: {cpu_percent:.1f}%")
                    
                    # 获取内存使用率
                    memory = psutil.virtual_memory()
                    self.memory_label.configure(text=f"内存: {memory.percent:.1f}%")
                    
                    # 检查网络状态
                    try:
                        import socket
                        socket.create_connection(("8.8.8.8", 53), timeout=3)
                        self.network_label.configure(text="网络: 正常")
                    except:
                        self.network_label.configure(text="网络: 异常")
                    
                    # 更新在线用户数（这里需要从数据库获取实际数据）
                    # self.update_online_users()
                    
                except Exception as e:
                    print(f"系统监控错误: {e}")
                
                time.sleep(5)  # 每5秒更新一次
                
        monitor_thread = threading.Thread(target=monitor_system, daemon=True)
        monitor_thread.start()
        
    def update_status(self, text):
        """更新状态文本"""
        self.status_text = text
        self.status_label.configure(text=text)
        
    def update_online_users(self, count=0):
        """更新在线用户数"""
        self.users_label.configure(text=f"在线: {count}")
        
    def show_progress(self, text, progress=0):
        """显示进度信息"""
        if progress > 0:
            self.update_status(f"{text} ({progress}%)")
        else:
            self.update_status(text)
            
    def show_error(self, text):
        """显示错误信息"""
        self.status_label.configure(
            text=f"❌ {text}",
            text_color=GUIConfig.COLORS["danger"]
        )
        # 5秒后恢复正常状态
        self.parent.after(5000, lambda: self.status_label.configure(
            text=self.status_text,
            text_color="white"
        ))
        
    def show_success(self, text):
        """显示成功信息"""
        self.status_label.configure(
            text=f"✅ {text}",
            text_color=GUIConfig.COLORS["success"]
        )
        # 3秒后恢复正常状态
        self.parent.after(3000, lambda: self.status_label.configure(
            text=self.status_text,
            text_color="white"
        ))
        
    def show_warning(self, text):
        """显示警告信息"""
        self.status_label.configure(
            text=f"⚠️ {text}",
            text_color=GUIConfig.COLORS["warning"]
        )
        # 4秒后恢复正常状态
        self.parent.after(4000, lambda: self.status_label.configure(
            text=self.status_text,
            text_color="white"
        ))
