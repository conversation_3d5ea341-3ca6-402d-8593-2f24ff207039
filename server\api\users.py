"""
用户相关API
"""
from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity

from server.database.models import db, User, UserGroup, UserPermission
from server.core.utils import get_client_ip, log_user_activity

users_bp = Blueprint('users', __name__)

@users_bp.route('/profile', methods=['PUT'])
@jwt_required()
def update_profile():
    """更新用户资料"""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()
        
        user = User.query.get(user_id)
        if not user:
            return jsonify({
                'success': False,
                'message': '用户不存在'
            }), 404
        
        # 更新允许的字段
        if 'real_name' in data:
            user.real_name = data['real_name'].strip()
        
        if 'email' in data:
            email = data['email'].strip()
            if email and User.query.filter(User.email == email, User.id != user_id).first():
                return jsonify({
                    'success': False,
                    'message': '邮箱已被其他用户使用'
                }), 400
            user.email = email
        
        db.session.commit()
        
        log_user_activity(
            user_id,
            'PROFILE_UPDATE',
            '用户更新个人资料'
        )
        
        return jsonify({
            'success': True,
            'message': '资料更新成功'
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"更新用户资料错误: {e}")
        return jsonify({
            'success': False,
            'message': '更新资料失败'
        }), 500

@users_bp.route('/permissions', methods=['GET'])
@jwt_required()
def get_user_permissions():
    """获取用户权限"""
    try:
        user_id = get_jwt_identity()
        
        user = User.query.get(user_id)
        if not user:
            return jsonify({
                'success': False,
                'message': '用户不存在'
            }), 404
        
        # 获取用户的所有权限
        permissions = UserPermission.query.filter_by(user_id=user_id).all()
        
        result = []
        for perm in permissions:
            result.append({
                'directory_id': perm.shared_dir_id,
                'directory_name': perm.shared_directory.name,
                'can_read': perm.can_read,
                'can_write': perm.can_write,
                'can_delete': perm.can_delete,
                'can_download': perm.can_download,
                'can_upload': perm.can_upload
            })
        
        return jsonify({
            'success': True,
            'data': {
                'is_admin': user.is_admin(),
                'group': user.group.group_name if user.group else None,
                'permissions': result
            }
        })
        
    except Exception as e:
        current_app.logger.error(f"获取用户权限错误: {e}")
        return jsonify({
            'success': False,
            'message': '获取权限失败'
        }), 500

users_bp = Blueprint('users', __name__)
