<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业文件共享系统 - 开发文档</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
            border-left: 4px solid #3498db;
            padding-left: 15px;
        }
        h3 {
            color: #2c3e50;
            margin-top: 25px;
        }
        .toc {
            background: #ecf0f1;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        .toc li {
            margin: 5px 0;
        }
        .toc a {
            text-decoration: none;
            color: #2980b9;
        }
        .toc a:hover {
            color: #3498db;
        }
        code {
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Consolas', monospace;
        }
        pre {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
        .feature-box {
            background: #e8f6f3;
            border: 1px solid #16a085;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .warning-box {
            background: #fdf2e9;
            border: 1px solid #e67e22;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .info-box {
            background: #ebf3fd;
            border: 1px solid #3498db;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #3498db;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        .architecture-diagram {
            text-align: center;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏢 企业文件共享系统 - 开发文档</h1>
        
        <div class="info-box">
            <strong>📋 文档版本:</strong> v1.0.0<br>
            <strong>📅 更新日期:</strong> 2024年1月<br>
            <strong>👨‍💻 开发团队:</strong> 企业文件共享系统开发组
        </div>

        <div class="toc">
            <h3>📚 目录</h3>
            <ul>
                <li><a href="#overview">1. 系统概述</a></li>
                <li><a href="#architecture">2. 系统架构</a></li>
                <li><a href="#features">3. 核心功能</a></li>
                <li><a href="#tech-stack">4. 技术栈</a></li>
                <li><a href="#installation">5. 安装部署</a></li>
                <li><a href="#development">6. 开发指南</a></li>
                <li><a href="#api">7. API文档</a></li>
                <li><a href="#database">8. 数据库设计</a></li>
                <li><a href="#security">9. 安全机制</a></li>
                <li><a href="#performance">10. 性能优化</a></li>
                <li><a href="#deployment">11. 生产部署</a></li>
                <li><a href="#maintenance">12. 运维管理</a></li>
            </ul>
        </div>

        <h2 id="overview">1. 系统概述</h2>
        
        <p>企业文件共享系统是一个专为企业内部设计的高性能文件管理和共享平台。系统支持内外网访问控制、双搜索引擎、实时监控、缩略图生成、加密下载等企业级功能。</p>

        <div class="feature-box">
            <h3>🎯 设计目标</h3>
            <ul>
                <li><strong>高性能:</strong> 支持大量并发用户和海量文件</li>
                <li><strong>安全性:</strong> 多层权限控制和数据加密</li>
                <li><strong>易用性:</strong> 直观的用户界面和管理工具</li>
                <li><strong>可扩展:</strong> 模块化设计，便于功能扩展</li>
                <li><strong>稳定性:</strong> 7x24小时稳定运行</li>
            </ul>
        </div>

        <h2 id="architecture">2. 系统架构</h2>

        <div class="architecture-diagram">
            <h3>🏗️ 系统架构图</h3>
            <pre>
┌─────────────────────────────────────────────────────────────┐
│                    前端层 (Frontend)                        │
├─────────────────────┬───────────────────┬───────────────────┤
│   管理员界面         │    用户界面        │   客户端程序       │
│   (Admin Web)       │   (User Web)      │   (Desktop App)   │
└─────────────────────┴───────────────────┴───────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    API网关层 (API Gateway)                  │
├─────────────────────────────────────────────────────────────┤
│  认证授权  │  请求路由  │  限流控制  │  日志记录  │  监控统计  │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                   业务逻辑层 (Business Logic)               │
├─────────────┬─────────────┬─────────────┬─────────────┬─────┤
│  用户管理    │  文件管理    │  搜索引擎    │  权限控制    │监控 │
│  (User)     │  (File)     │  (Search)   │  (Auth)     │(Mon)│
└─────────────┴─────────────┴─────────────┴─────────────┴─────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                   数据访问层 (Data Access)                  │
├─────────────┬─────────────┬─────────────┬─────────────┬─────┤
│  MySQL      │  文件存储    │  缓存系统    │  搜索索引    │日志 │
│  (Database) │  (Storage)  │  (Cache)    │  (Index)    │(Log)│
└─────────────┴─────────────┴─────────────┴─────────────┴─────┘
            </pre>
        </div>

        <h3>🔧 架构特点</h3>
        <ul>
            <li><strong>前后端分离:</strong> 管理端和用户端完全独立</li>
            <li><strong>微服务设计:</strong> 模块化的业务逻辑层</li>
            <li><strong>RESTful API:</strong> 标准化的接口设计</li>
            <li><strong>数据库分离:</strong> 业务数据与文件存储分离</li>
            <li><strong>可扩展性:</strong> 支持水平扩展和负载均衡</li>
        </ul>

        <h2 id="features">3. 核心功能</h2>

        <h3>👥 用户管理</h3>
        <div class="feature-box">
            <ul>
                <li>用户注册与认证（支持注册码机制）</li>
                <li>用户组管理（管理员、高级用户、普通用户、只读用户）</li>
                <li>权限控制（读取、写入、删除、下载、上传）</li>
                <li>在线用户监控</li>
                <li>用户行为日志</li>
            </ul>
        </div>

        <h3>📁 文件管理</h3>
        <div class="feature-box">
            <ul>
                <li>多目录共享管理</li>
                <li>文件索引与监控</li>
                <li>缩略图生成（支持多种格式）</li>
                <li>文件加密下载</li>
                <li>批量下载与打包</li>
                <li>文件版本控制</li>
            </ul>
        </div>

        <h3>🔍 双搜索引擎</h3>
        <div class="feature-box">
            <ul>
                <li><strong>文件名搜索:</strong> Everything-like高速文件名搜索</li>
                <li><strong>图像搜索:</strong> 基于OpenCV的图像特征识别</li>
                <li>搜索结果权限过滤</li>
                <li>搜索历史记录</li>
                <li>智能搜索建议</li>
            </ul>
        </div>

        <h3>🔒 安全机制</h3>
        <div class="feature-box">
            <ul>
                <li>JWT令牌认证</li>
                <li>多级权限控制</li>
                <li>文件访问日志</li>
                <li>下载加密保护</li>
                <li>IP访问限制</li>
                <li>敏感文件监控</li>
            </ul>
        </div>

        <h2 id="tech-stack">4. 技术栈</h2>

        <table>
            <tr>
                <th>层次</th>
                <th>技术</th>
                <th>版本</th>
                <th>说明</th>
            </tr>
            <tr>
                <td>后端框架</td>
                <td>Flask</td>
                <td>2.3.3</td>
                <td>轻量级Web框架</td>
            </tr>
            <tr>
                <td>数据库</td>
                <td>MySQL</td>
                <td>5.7+</td>
                <td>关系型数据库</td>
            </tr>
            <tr>
                <td>ORM</td>
                <td>SQLAlchemy</td>
                <td>3.0.5</td>
                <td>数据库ORM框架</td>
            </tr>
            <tr>
                <td>认证</td>
                <td>JWT</td>
                <td>4.5.3</td>
                <td>JSON Web Token</td>
            </tr>
            <tr>
                <td>GUI框架</td>
                <td>CustomTkinter</td>
                <td>5.2.0</td>
                <td>现代化桌面界面</td>
            </tr>
            <tr>
                <td>图像处理</td>
                <td>Pillow + OpenCV</td>
                <td>10.0.1 + 4.8.1</td>
                <td>图像处理和识别</td>
            </tr>
            <tr>
                <td>搜索引擎</td>
                <td>Whoosh</td>
                <td>2.7.4</td>
                <td>全文搜索引擎</td>
            </tr>
            <tr>
                <td>系统监控</td>
                <td>psutil</td>
                <td>5.9.6</td>
                <td>系统资源监控</td>
            </tr>
        </table>

        <h2 id="installation">5. 安装部署</h2>

        <h3>📋 系统要求</h3>
        <div class="info-box">
            <ul>
                <li><strong>操作系统:</strong> Windows 10/11, Windows Server 2016+</li>
                <li><strong>Python:</strong> 3.8或更高版本</li>
                <li><strong>MySQL:</strong> 5.7或更高版本</li>
                <li><strong>内存:</strong> 最低4GB，推荐8GB+</li>
                <li><strong>磁盘:</strong> 系统盘10GB+，数据盘根据需求</li>
                <li><strong>网络:</strong> 千兆网卡（推荐）</li>
            </ul>
        </div>

        <h3>🚀 快速安装</h3>
        <pre><code># 1. 克隆项目
git clone https://github.com/your-repo/file-sharing-system.git
cd file-sharing-system

# 2. 运行安装脚本
python install.py

# 3. 启动服务端GUI
python run_server.py gui

# 4. 或启动API服务
python run_server.py api</code></pre>

        <h3>⚙️ 手动安装</h3>
        <pre><code># 1. 安装依赖
pip install -r requirements.txt

# 2. 配置数据库
# 编辑 server/config/config.py 中的数据库配置

# 3. 初始化数据库
python run_server.py init-db

# 4. 创建必要目录
mkdir uploads thumbnails temp logs search_index

# 5. 启动服务
python run_server.py gui</code></pre>

        <h2 id="development">6. 开发指南</h2>

        <h3>📁 项目结构</h3>
        <pre><code>Net/
├── server/                 # 服务端代码
│   ├── gui/               # GUI界面
│   │   ├── components/    # 界面组件
│   │   ├── pages/         # 页面模块
│   │   └── main_window.py # 主窗口
│   ├── api/               # API接口
│   │   ├── auth.py        # 认证接口
│   │   ├── files.py       # 文件接口
│   │   ├── users.py       # 用户接口
│   │   ├── search.py      # 搜索接口
│   │   ├── admin.py       # 管理接口
│   │   └── app.py         # Flask应用
│   ├── core/              # 核心功能
│   │   └── utils.py       # 工具函数
│   ├── database/          # 数据库相关
│   │   ├── models.py      # 数据模型
│   │   └── schema.sql     # 数据库结构
│   └── config/            # 配置文件
│       └── config.py      # 系统配置
├── client/                # 客户端程序（待开发）
├── web/                   # Web前端（待开发）
├── docs/                  # 文档
├── tests/                 # 测试文件
├── requirements.txt       # 依赖列表
├── run_server.py         # 启动脚本
└── install.py            # 安装脚本</code></pre>

        <h3>🔧 开发环境配置</h3>
        <pre><code># 1. 创建虚拟环境
python -m venv venv
venv\Scripts\activate  # Windows
source venv/bin/activate  # Linux/Mac

# 2. 安装开发依赖
pip install -r requirements.txt
pip install pytest black flake8  # 开发工具

# 3. 配置IDE
# 推荐使用VSCode或PyCharm
# 配置Python解释器为虚拟环境中的Python

# 4. 运行测试
pytest tests/

# 5. 代码格式化
black server/
flake8 server/</code></pre>

        <h3>📝 编码规范</h3>
        <div class="warning-box">
            <ul>
                <li>遵循PEP 8 Python编码规范</li>
                <li>使用类型注解提高代码可读性</li>
                <li>编写完整的文档字符串</li>
                <li>单元测试覆盖率不低于80%</li>
                <li>提交前运行代码格式化工具</li>
                <li>使用有意义的变量和函数名</li>
                <li>避免硬编码，使用配置文件</li>
            </ul>
        </div>

        <h2 id="api">7. API文档</h2>

        <h3>🔐 认证接口</h3>
        <table>
            <tr>
                <th>接口</th>
                <th>方法</th>
                <th>说明</th>
            </tr>
            <tr>
                <td>/api/v1/auth/login</td>
                <td>POST</td>
                <td>用户登录</td>
            </tr>
            <tr>
                <td>/api/v1/auth/logout</td>
                <td>POST</td>
                <td>用户登出</td>
            </tr>
            <tr>
                <td>/api/v1/auth/register</td>
                <td>POST</td>
                <td>用户注册</td>
            </tr>
            <tr>
                <td>/api/v1/auth/refresh</td>
                <td>POST</td>
                <td>刷新令牌</td>
            </tr>
            <tr>
                <td>/api/v1/auth/profile</td>
                <td>GET</td>
                <td>获取用户信息</td>
            </tr>
        </table>

        <h3>📁 文件接口</h3>
        <table>
            <tr>
                <th>接口</th>
                <th>方法</th>
                <th>说明</th>
            </tr>
            <tr>
                <td>/api/v1/files/list</td>
                <td>GET</td>
                <td>获取文件列表</td>
            </tr>
            <tr>
                <td>/api/v1/files/download/{id}</td>
                <td>GET</td>
                <td>下载单个文件</td>
            </tr>
            <tr>
                <td>/api/v1/files/download/batch</td>
                <td>POST</td>
                <td>批量下载文件</td>
            </tr>
            <tr>
                <td>/api/v1/files/thumbnail/{id}</td>
                <td>GET</td>
                <td>获取缩略图</td>
            </tr>
            <tr>
                <td>/api/v1/files/info/{id}</td>
                <td>GET</td>
                <td>获取文件信息</td>
            </tr>
        </table>

        <h3>🔍 搜索接口</h3>
        <table>
            <tr>
                <th>接口</th>
                <th>方法</th>
                <th>说明</th>
            </tr>
            <tr>
                <td>/api/v1/search/files</td>
                <td>GET</td>
                <td>搜索文件</td>
            </tr>
            <tr>
                <td>/api/v1/search/suggestions</td>
                <td>GET</td>
                <td>获取搜索建议</td>
            </tr>
        </table>

        <div class="info-box">
            <strong>📖 API详细文档:</strong> 完整的API文档请参考 <code>docs/api_reference.html</code>
        </div>

        <h2 id="database">8. 数据库设计</h2>

        <h3>📊 核心表结构</h3>
        <div class="feature-box">
            <ul>
                <li><strong>users:</strong> 用户表</li>
                <li><strong>user_groups:</strong> 用户组表</li>
                <li><strong>shared_directories:</strong> 共享目录表</li>
                <li><strong>file_index:</strong> 文件索引表</li>
                <li><strong>user_permissions:</strong> 用户权限表</li>
                <li><strong>download_logs:</strong> 下载记录表</li>
                <li><strong>search_logs:</strong> 搜索记录表</li>
                <li><strong>system_logs:</strong> 系统日志表</li>
                <li><strong>online_users:</strong> 在线用户表</li>
                <li><strong>system_config:</strong> 系统配置表</li>
            </ul>
        </div>

        <h3>🔗 表关系图</h3>
        <pre><code>users ──┬── user_permissions ── shared_directories
        │                              │
        ├── download_logs               ├── file_index
        ├── search_logs                 │
        ├── system_logs                 │
        ├── online_users                │
        └── user_groups                 └── (文件系统)</code></pre>

        <h2 id="security">9. 安全机制</h2>

        <h3>🛡️ 多层安全防护</h3>
        <div class="warning-box">
            <ul>
                <li><strong>认证层:</strong> JWT令牌 + 会话管理</li>
                <li><strong>授权层:</strong> 基于角色的权限控制(RBAC)</li>
                <li><strong>传输层:</strong> HTTPS加密传输</li>
                <li><strong>存储层:</strong> 文件加密存储</li>
                <li><strong>访问层:</strong> IP白名单 + 访问频率限制</li>
                <li><strong>审计层:</strong> 完整的操作日志记录</li>
            </ul>
        </div>

        <h3>🔐 权限控制矩阵</h3>
        <table>
            <tr>
                <th>用户组</th>
                <th>读取</th>
                <th>下载</th>
                <th>上传</th>
                <th>删除</th>
                <th>管理</th>
            </tr>
            <tr>
                <td>管理员</td>
                <td>✅</td>
                <td>✅</td>
                <td>✅</td>
                <td>✅</td>
                <td>✅</td>
            </tr>
            <tr>
                <td>高级用户</td>
                <td>✅</td>
                <td>✅</td>
                <td>✅</td>
                <td>❌</td>
                <td>❌</td>
            </tr>
            <tr>
                <td>普通用户</td>
                <td>✅</td>
                <td>✅</td>
                <td>❌</td>
                <td>❌</td>
                <td>❌</td>
            </tr>
            <tr>
                <td>只读用户</td>
                <td>✅</td>
                <td>❌</td>
                <td>❌</td>
                <td>❌</td>
                <td>❌</td>
            </tr>
        </table>

        <div class="warning-box">
            <strong>⚠️ 安全注意事项:</strong>
            <ul>
                <li>定期更新系统和依赖包</li>
                <li>使用强密码策略</li>
                <li>定期备份数据库和配置</li>
                <li>监控异常访问行为</li>
                <li>及时处理安全告警</li>
            </ul>
        </div>

        <h2 id="performance">10. 性能优化</h2>

        <h3>⚡ 性能优化策略</h3>
        <div class="feature-box">
            <ul>
                <li><strong>数据库优化:</strong> 索引优化、查询优化、连接池</li>
                <li><strong>文件处理:</strong> 异步处理、缓存机制、CDN加速</li>
                <li><strong>搜索优化:</strong> 索引预建、结果缓存、分页加载</li>
                <li><strong>网络优化:</strong> 压缩传输、断点续传、并发控制</li>
                <li><strong>系统优化:</strong> 内存管理、进程池、负载均衡</li>
            </ul>
        </div>

        <h3>📈 性能监控指标</h3>
        <table>
            <tr>
                <th>指标</th>
                <th>目标值</th>
                <th>监控方式</th>
            </tr>
            <tr>
                <td>响应时间</td>
                <td>&lt; 200ms</td>
                <td>API监控</td>
            </tr>
            <tr>
                <td>并发用户</td>
                <td>1000+</td>
                <td>负载测试</td>
            </tr>
            <tr>
                <td>文件传输</td>
                <td>100MB/s+</td>
                <td>网络监控</td>
            </tr>
            <tr>
                <td>搜索速度</td>
                <td>&lt; 1s</td>
                <td>搜索日志</td>
            </tr>
            <tr>
                <td>系统可用性</td>
                <td>99.9%</td>
                <td>健康检查</td>
            </tr>
        </table>

        <div class="info-box">
            <strong>💡 性能调优建议:</strong> 根据实际使用情况调整配置参数，定期进行性能测试和优化。
        </div>

        <p style="text-align: center; margin-top: 50px; color: #7f8c8d;">
            <strong>📧 技术支持:</strong> <EMAIL> | 
            <strong>📞 联系电话:</strong> 400-xxx-xxxx<br>
            <em>© 2024 企业文件共享系统开发团队. 保留所有权利.</em>
        </p>
    </div>
</body>
</html>
