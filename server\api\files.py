"""
文件相关API
"""
from flask import Blueprint, request, jsonify, send_file, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from werkzeug.utils import secure_filename
import os
import zipfile
import tempfile
from datetime import datetime

from server.database.models import db, User, FileIndex, SharedDirectory, DownloadLog
from server.core.utils import get_client_ip, check_file_permission

files_bp = Blueprint('files', __name__)

@files_bp.route('/list', methods=['GET'])
@jwt_required()
def list_files():
    """获取文件列表"""
    try:
        user_id = get_jwt_identity()
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        directory_id = request.args.get('directory_id', type=int)
        
        # 构建查询
        query = FileIndex.query
        
        if directory_id:
            query = query.filter_by(shared_dir_id=directory_id)
        
        # 分页
        pagination = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )
        
        files = []
        for file in pagination.items:
            # 检查用户权限
            if check_file_permission(user_id, file.shared_dir_id, 'read'):
                files.append({
                    'id': file.id,
                    'name': file.file_name,
                    'path': file.file_path,
                    'size': file.file_size,
                    'type': file.file_type,
                    'has_thumbnail': file.has_thumbnail,
                    'thumbnail_path': file.thumbnail_path,
                    'last_modified': file.last_modified.isoformat() if file.last_modified else None,
                    'directory': file.shared_directory.name
                })
        
        return jsonify({
            'success': True,
            'data': {
                'files': files,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': pagination.total,
                    'pages': pagination.pages
                }
            }
        })
        
    except Exception as e:
        current_app.logger.error(f"获取文件列表错误: {e}")
        return jsonify({
            'success': False,
            'message': '获取文件列表失败'
        }), 500

@files_bp.route('/download/<int:file_id>', methods=['GET'])
@jwt_required()
def download_file(file_id):
    """下载单个文件"""
    try:
        user_id = get_jwt_identity()
        file = FileIndex.query.get_or_404(file_id)
        
        # 检查下载权限
        if not check_file_permission(user_id, file.shared_dir_id, 'download'):
            return jsonify({
                'success': False,
                'message': '没有下载权限'
            }), 403
        
        # 构建完整文件路径
        full_path = file.get_full_path()
        
        if not os.path.exists(full_path):
            return jsonify({
                'success': False,
                'message': '文件不存在'
            }), 404
        
        # 记录下载日志
        download_log = DownloadLog(
            user_id=user_id,
            file_id=file_id,
            file_path=file.file_path,
            download_type='single',
            file_count=1,
            total_size=file.file_size,
            client_ip=get_client_ip(),
            user_agent=request.headers.get('User-Agent', '')
        )
        db.session.add(download_log)
        db.session.commit()
        
        return send_file(
            full_path,
            as_attachment=True,
            download_name=file.file_name
        )
        
    except Exception as e:
        current_app.logger.error(f"下载文件错误: {e}")
        return jsonify({
            'success': False,
            'message': '下载文件失败'
        }), 500

@files_bp.route('/download/batch', methods=['POST'])
@jwt_required()
def download_batch():
    """批量下载文件"""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()
        file_ids = data.get('file_ids', [])
        
        if not file_ids:
            return jsonify({
                'success': False,
                'message': '请选择要下载的文件'
            }), 400
        
        # 检查批量下载限制
        max_batch_files = current_app.config.get('MAX_BATCH_FILES', 100)
        if len(file_ids) > max_batch_files:
            return jsonify({
                'success': False,
                'message': f'批量下载文件数量不能超过{max_batch_files}个'
            }), 400
        
        # 获取文件信息
        files = FileIndex.query.filter(FileIndex.id.in_(file_ids)).all()
        
        # 检查权限和文件存在性
        valid_files = []
        total_size = 0
        
        for file in files:
            if check_file_permission(user_id, file.shared_dir_id, 'download'):
                full_path = file.get_full_path()
                if os.path.exists(full_path):
                    valid_files.append(file)
                    total_size += file.file_size or 0
        
        if not valid_files:
            return jsonify({
                'success': False,
                'message': '没有可下载的文件'
            }), 400
        
        # 检查总大小限制
        max_download_size = current_app.config.get('MAX_DOWNLOAD_SIZE', 1024*1024*1024)
        if total_size > max_download_size:
            return jsonify({
                'success': False,
                'message': f'下载文件总大小不能超过{max_download_size//1024//1024}MB'
            }), 400
        
        # 创建临时ZIP文件
        temp_dir = tempfile.mkdtemp()
        zip_filename = f"batch_download_{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip"
        zip_path = os.path.join(temp_dir, zip_filename)
        
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for file in valid_files:
                full_path = file.get_full_path()
                # 使用相对路径作为ZIP内的文件名
                arcname = f"{file.shared_directory.name}/{file.file_name}"
                zipf.write(full_path, arcname)
        
        # 记录下载日志
        download_log = DownloadLog(
            user_id=user_id,
            download_type='batch',
            file_count=len(valid_files),
            total_size=total_size,
            client_ip=get_client_ip(),
            user_agent=request.headers.get('User-Agent', '')
        )
        db.session.add(download_log)
        db.session.commit()
        
        return send_file(
            zip_path,
            as_attachment=True,
            download_name=zip_filename,
            mimetype='application/zip'
        )
        
    except Exception as e:
        current_app.logger.error(f"批量下载错误: {e}")
        return jsonify({
            'success': False,
            'message': '批量下载失败'
        }), 500

@files_bp.route('/thumbnail/<int:file_id>', methods=['GET'])
@jwt_required()
def get_thumbnail(file_id):
    """获取文件缩略图"""
    try:
        user_id = get_jwt_identity()
        file = FileIndex.query.get_or_404(file_id)
        
        # 检查读取权限
        if not check_file_permission(user_id, file.shared_dir_id, 'read'):
            return jsonify({
                'success': False,
                'message': '没有访问权限'
            }), 403
        
        if not file.has_thumbnail or not file.thumbnail_path:
            return jsonify({
                'success': False,
                'message': '缩略图不存在'
            }), 404
        
        thumbnail_path = os.path.join(
            current_app.config['THUMBNAIL_FOLDER'],
            file.thumbnail_path
        )
        
        if not os.path.exists(thumbnail_path):
            return jsonify({
                'success': False,
                'message': '缩略图文件不存在'
            }), 404
        
        return send_file(thumbnail_path)
        
    except Exception as e:
        current_app.logger.error(f"获取缩略图错误: {e}")
        return jsonify({
            'success': False,
            'message': '获取缩略图失败'
        }), 500

@files_bp.route('/directories', methods=['GET'])
@jwt_required()
def list_directories():
    """获取共享目录列表"""
    try:
        user_id = get_jwt_identity()
        
        # 获取用户有权限访问的目录
        directories = SharedDirectory.query.filter_by(is_active=True).all()
        
        result = []
        for directory in directories:
            if check_file_permission(user_id, directory.id, 'read'):
                result.append({
                    'id': directory.id,
                    'name': directory.name,
                    'description': directory.description,
                    'file_count': directory.files.count(),
                    'allow_internal': directory.allow_internal,
                    'allow_external': directory.allow_external
                })
        
        return jsonify({
            'success': True,
            'data': result
        })
        
    except Exception as e:
        current_app.logger.error(f"获取目录列表错误: {e}")
        return jsonify({
            'success': False,
            'message': '获取目录列表失败'
        }), 500

@files_bp.route('/info/<int:file_id>', methods=['GET'])
@jwt_required()
def get_file_info(file_id):
    """获取文件详细信息"""
    try:
        user_id = get_jwt_identity()
        file = FileIndex.query.get_or_404(file_id)
        
        # 检查读取权限
        if not check_file_permission(user_id, file.shared_dir_id, 'read'):
            return jsonify({
                'success': False,
                'message': '没有访问权限'
            }), 403
        
        return jsonify({
            'success': True,
            'data': {
                'id': file.id,
                'name': file.file_name,
                'path': file.file_path,
                'size': file.file_size,
                'type': file.file_type,
                'hash': file.file_hash,
                'has_thumbnail': file.has_thumbnail,
                'last_modified': file.last_modified.isoformat() if file.last_modified else None,
                'indexed_at': file.indexed_at.isoformat(),
                'directory': {
                    'id': file.shared_directory.id,
                    'name': file.shared_directory.name,
                    'description': file.shared_directory.description
                }
            }
        })
        
    except Exception as e:
        current_app.logger.error(f"获取文件信息错误: {e}")
        return jsonify({
            'success': False,
            'message': '获取文件信息失败'
        }), 500
