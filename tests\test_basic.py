"""
基础功能测试
"""
import unittest
import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class TestBasicFunctionality(unittest.TestCase):
    """基础功能测试类"""
    
    def test_import_modules(self):
        """测试模块导入"""
        try:
            from server.config.config import Config
            from server.database.models import User, FileIndex
            from server.core.utils import validate_password
            self.assertTrue(True, "模块导入成功")
        except ImportError as e:
            self.fail(f"模块导入失败: {e}")
    
    def test_config_loading(self):
        """测试配置加载"""
        from server.config.config import Config
        
        self.assertIsNotNone(Config.APP_NAME)
        self.assertIsNotNone(Config.DB_HOST)
        self.assertIsNotNone(Config.DB_USER)
        self.assertTrue(Config.PASSWORD_MIN_LENGTH > 0)
    
    def test_password_validation(self):
        """测试密码验证"""
        from server.core.utils import validate_password
        
        # 测试有效密码
        self.assertTrue(validate_password("123456"))
        self.assertTrue(validate_password("password123"))
        
        # 测试无效密码
        self.assertFalse(validate_password("123"))
        self.assertFalse(validate_password(""))
    
    def test_file_utils(self):
        """测试文件工具函数"""
        from server.core.utils import (
            format_file_size, 
            sanitize_filename,
            is_image_file
        )
        
        # 测试文件大小格式化
        self.assertEqual(format_file_size(0), "0 B")
        self.assertEqual(format_file_size(1024), "1.0 KB")
        self.assertEqual(format_file_size(1024*1024), "1.0 MB")
        
        # 测试文件名清理
        self.assertEqual(sanitize_filename("test<>file.txt"), "test__file.txt")
        self.assertEqual(sanitize_filename(""), "unnamed_file")
        
        # 测试图像文件检测
        self.assertTrue(is_image_file("test.jpg"))
        self.assertTrue(is_image_file("test.png"))
        self.assertFalse(is_image_file("test.txt"))
    
    def test_directory_structure(self):
        """测试目录结构"""
        required_dirs = [
            "server",
            "server/gui", 
            "server/api",
            "server/core",
            "server/database",
            "server/config"
        ]
        
        for dir_path in required_dirs:
            full_path = project_root / dir_path
            self.assertTrue(full_path.exists(), f"目录不存在: {dir_path}")
    
    def test_required_files(self):
        """测试必需文件"""
        required_files = [
            "requirements.txt",
            "run_server.py",
            "install.py",
            "server/config/config.py",
            "server/database/models.py",
            "server/database/schema.sql",
            "server/api/app.py"
        ]
        
        for file_path in required_files:
            full_path = project_root / file_path
            self.assertTrue(full_path.exists(), f"文件不存在: {file_path}")

class TestDatabaseModels(unittest.TestCase):
    """数据库模型测试"""
    
    def test_user_model(self):
        """测试用户模型"""
        from server.database.models import User
        
        # 测试用户创建
        user = User(username="testuser", email="<EMAIL>")
        self.assertEqual(user.username, "testuser")
        self.assertEqual(user.email, "<EMAIL>")
        
        # 测试密码设置和验证
        user.set_password("testpassword")
        self.assertTrue(user.check_password("testpassword"))
        self.assertFalse(user.check_password("wrongpassword"))
    
    def test_file_index_model(self):
        """测试文件索引模型"""
        from server.database.models import FileIndex
        
        file_index = FileIndex(
            file_name="test.jpg",
            file_path="/path/to/test.jpg",
            file_size=1024,
            file_type="image/jpeg"
        )
        
        self.assertEqual(file_index.file_name, "test.jpg")
        self.assertEqual(file_index.file_size, 1024)
        self.assertEqual(file_index.file_type, "image/jpeg")

if __name__ == '__main__':
    # 创建测试套件
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # 添加测试类
    suite.addTests(loader.loadTestsFromTestCase(TestBasicFunctionality))
    suite.addTests(loader.loadTestsFromTestCase(TestDatabaseModels))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出结果
    if result.wasSuccessful():
        print("\n✅ 所有测试通过！")
        sys.exit(0)
    else:
        print(f"\n❌ 测试失败: {len(result.failures)} 个失败, {len(result.errors)} 个错误")
        sys.exit(1)
