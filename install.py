#!/usr/bin/env python3
"""
安装脚本
"""
import os
import sys
import subprocess
import platform
from pathlib import Path

def run_command(command, description):
    """运行命令"""
    print(f"正在{description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description}成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description}失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ 需要Python 3.8或更高版本")
        print(f"当前版本: {version.major}.{version.minor}.{version.micro}")
        return False
    
    print(f"✅ Python版本检查通过: {version.major}.{version.minor}.{version.micro}")
    return True

def check_mysql():
    """检查MySQL是否可用"""
    try:
        import pymysql
        from server.config.config import Config
        
        connection = pymysql.connect(
            host=Config.DB_HOST,
            port=Config.DB_PORT,
            user=Config.DB_USER,
            password=Config.DB_PASSWORD,
            charset=Config.DB_CHARSET
        )
        connection.close()
        print("✅ MySQL连接测试成功")
        return True
    except ImportError:
        print("⚠️ PyMySQL未安装，将在依赖安装时处理")
        return True
    except Exception as e:
        print(f"❌ MySQL连接失败: {e}")
        print("请检查MySQL服务是否运行，以及配置是否正确")
        return False

def install_dependencies():
    """安装依赖"""
    requirements_file = Path(__file__).parent / "requirements.txt"
    
    if not requirements_file.exists():
        print("❌ requirements.txt文件不存在")
        return False
    
    # 升级pip
    if not run_command(f"{sys.executable} -m pip install --upgrade pip", "升级pip"):
        return False
    
    # 安装依赖
    if not run_command(f"{sys.executable} -m pip install -r {requirements_file}", "安装依赖包"):
        return False
    
    return True

def create_directories():
    """创建必要的目录"""
    directories = [
        "uploads",
        "thumbnails", 
        "temp",
        "logs",
        "search_index"
    ]
    
    for directory in directories:
        dir_path = Path(__file__).parent / directory
        try:
            dir_path.mkdir(exist_ok=True)
            print(f"✅ 创建目录: {directory}")
        except Exception as e:
            print(f"❌ 创建目录失败 {directory}: {e}")
            return False
    
    return True

def setup_database():
    """设置数据库"""
    print("正在初始化数据库...")
    
    try:
        # 运行数据库初始化
        result = subprocess.run(
            [sys.executable, "run_server.py", "init-db"],
            capture_output=True,
            text=True,
            cwd=Path(__file__).parent
        )
        
        if result.returncode == 0:
            print("✅ 数据库初始化成功")
            return True
        else:
            print(f"❌ 数据库初始化失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 数据库初始化异常: {e}")
        return False

def create_desktop_shortcut():
    """创建桌面快捷方式（Windows）"""
    if platform.system() != "Windows":
        return True
    
    try:
        import winshell
        from win32com.client import Dispatch
        
        desktop = winshell.desktop()
        path = os.path.join(desktop, "文件共享系统.lnk")
        target = os.path.join(os.getcwd(), "run_server.py")
        wDir = os.getcwd()
        icon = target
        
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(path)
        shortcut.Targetpath = sys.executable
        shortcut.Arguments = f'"{target}" gui'
        shortcut.WorkingDirectory = wDir
        shortcut.IconLocation = icon
        shortcut.save()
        
        print("✅ 桌面快捷方式创建成功")
        return True
        
    except ImportError:
        print("⚠️ 无法创建桌面快捷方式（缺少winshell或pywin32）")
        return True
    except Exception as e:
        print(f"⚠️ 创建桌面快捷方式失败: {e}")
        return True

def main():
    """主安装流程"""
    print("=" * 60)
    print("企业文件共享系统 - 安装程序")
    print("=" * 60)
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 安装依赖
    print("\n步骤 1: 安装依赖包")
    if not install_dependencies():
        print("❌ 依赖安装失败，请检查网络连接和pip配置")
        sys.exit(1)
    
    # 创建目录
    print("\n步骤 2: 创建必要目录")
    if not create_directories():
        print("❌ 目录创建失败")
        sys.exit(1)
    
    # 检查MySQL
    print("\n步骤 3: 检查数据库连接")
    if not check_mysql():
        print("❌ 数据库连接失败，请检查MySQL配置")
        print("请确保MySQL服务正在运行，并且配置文件中的数据库连接信息正确")
        sys.exit(1)
    
    # 初始化数据库
    print("\n步骤 4: 初始化数据库")
    if not setup_database():
        print("❌ 数据库初始化失败")
        sys.exit(1)
    
    # 创建快捷方式
    print("\n步骤 5: 创建快捷方式")
    create_desktop_shortcut()
    
    print("\n" + "=" * 60)
    print("🎉 安装完成！")
    print("=" * 60)
    print("\n启动方式:")
    print("1. GUI管理界面: python run_server.py gui")
    print("2. API服务: python run_server.py api")
    print("3. 检查依赖: python run_server.py check")
    print("\n默认管理员账户:")
    print("用户名: admin")
    print("密码: admin123")
    print("\n请及时修改默认密码！")
    print("\n" + "=" * 60)

if __name__ == '__main__':
    main()
