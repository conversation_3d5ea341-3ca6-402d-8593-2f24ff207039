"""
服务端配置文件
"""
import os
from pathlib import Path

# 项目根目录
BASE_DIR = Path(__file__).parent.parent.parent

class Config:
    """基础配置"""
    # 应用配置
    APP_NAME = "企业文件共享系统"
    VERSION = "1.0.0"
    DEBUG = False
    
    # 数据库配置
    DB_HOST = "localhost"
    DB_PORT = 3306
    DB_USER = "root"
    DB_PASSWORD = "123456"
    DB_NAME = "file_sharing_system"
    DB_CHARSET = "utf8mb4"
    
    # 数据库连接字符串
    SQLALCHEMY_DATABASE_URI = f"mysql+pymysql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}?charset={DB_CHARSET}"
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_pre_ping': True,
        'pool_recycle': 300,
        'pool_timeout': 20,
        'max_overflow': 0
    }
    
    # JWT配置
    JWT_SECRET_KEY = "your-secret-key-change-in-production"
    JWT_ACCESS_TOKEN_EXPIRES = 3600  # 1小时
    JWT_REFRESH_TOKEN_EXPIRES = 86400 * 7  # 7天
    
    # 文件配置
    UPLOAD_FOLDER = BASE_DIR / "uploads"
    THUMBNAIL_FOLDER = BASE_DIR / "thumbnails"
    TEMP_FOLDER = BASE_DIR / "temp"
    LOG_FOLDER = BASE_DIR / "logs"
    
    # 文件大小限制
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024 * 1024  # 16GB
    MAX_DOWNLOAD_SIZE = 1024 * 1024 * 1024  # 1GB
    MAX_BATCH_FILES = 100
    
    # 缩略图配置
    THUMBNAIL_SIZE = (200, 200)
    THUMBNAIL_QUALITY = 80
    SUPPORTED_IMAGE_FORMATS = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp']
    SUPPORTED_DOCUMENT_FORMATS = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx']
    SUPPORTED_DESIGN_FORMATS = ['.psd', '.ai', '.eps', '.svg']
    
    # 搜索引擎配置
    SEARCH_INDEX_PATH = BASE_DIR / "search_index"
    ENABLE_FILENAME_SEARCH = True
    ENABLE_IMAGE_SEARCH = True
    IMAGE_SEARCH_THRESHOLD = 0.8
    
    # 安全配置
    SECRET_KEY = "your-secret-key-change-in-production"
    PASSWORD_MIN_LENGTH = 6
    MAX_LOGIN_ATTEMPTS = 5
    LOCKOUT_DURATION = 300  # 5分钟
    
    # 网络配置
    SERVER_HOST = "0.0.0.0"
    SERVER_PORT = 5000
    ENABLE_EXTERNAL_ACCESS = False
    ALLOWED_HOSTS = ["localhost", "127.0.0.1"]
    
    # 监控配置
    ENABLE_MONITORING = True
    LOG_LEVEL = "INFO"
    LOG_MAX_SIZE = 10 * 1024 * 1024  # 10MB
    LOG_BACKUP_COUNT = 5
    
    # 加密配置
    ENCRYPTION_KEY = b"your-32-byte-encryption-key-here"
    ENABLE_FILE_ENCRYPTION = True
    ENCRYPTION_THRESHOLD = 3  # 下载3次后加密
    
    # 通知配置
    ENABLE_NOTIFICATIONS = True
    NOTIFICATION_SCROLL_SPEED = 50
    
    @staticmethod
    def init_app(app):
        """初始化应用配置"""
        # 创建必要的目录
        for folder in [Config.UPLOAD_FOLDER, Config.THUMBNAIL_FOLDER, 
                      Config.TEMP_FOLDER, Config.LOG_FOLDER, Config.SEARCH_INDEX_PATH]:
            folder.mkdir(parents=True, exist_ok=True)

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    LOG_LEVEL = "DEBUG"

class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    LOG_LEVEL = "WARNING"

class TestingConfig(Config):
    """测试环境配置"""
    TESTING = True
    DB_NAME = "file_sharing_system_test"
    SQLALCHEMY_DATABASE_URI = f"mysql+pymysql://{Config.DB_USER}:{Config.DB_PASSWORD}@{Config.DB_HOST}:{Config.DB_PORT}/{DB_NAME}?charset={Config.DB_CHARSET}"

# 配置字典
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}

# GUI配置
class GUIConfig:
    """GUI界面配置"""
    # 窗口配置
    WINDOW_TITLE = "企业文件共享系统 - 服务端管理"
    WINDOW_MIN_WIDTH = 1200
    WINDOW_MIN_HEIGHT = 800
    WINDOW_ICON = BASE_DIR / "assets" / "icon.ico"
    
    # 主题配置
    THEME_MODE = "dark"  # "light", "dark", "system"
    COLOR_THEME = "blue"  # "blue", "green", "dark-blue"
    
    # 字体配置
    FONT_FAMILY = "Microsoft YaHei UI"
    FONT_SIZE = 12
    TITLE_FONT_SIZE = 16
    
    # 布局配置
    SIDEBAR_WIDTH = 250
    HEADER_HEIGHT = 60
    FOOTER_HEIGHT = 30
    PADDING = 10
    
    # 颜色配置
    COLORS = {
        "primary": "#1f538d",
        "secondary": "#14375e",
        "success": "#198754",
        "warning": "#ffc107",
        "danger": "#dc3545",
        "info": "#0dcaf0",
        "light": "#f8f9fa",
        "dark": "#212529"
    }
    
    # 图标配置
    ICONS = {
        "dashboard": "📊",
        "users": "👥",
        "files": "📁",
        "settings": "⚙️",
        "logs": "📋",
        "monitor": "📈",
        "search": "🔍",
        "download": "⬇️",
        "upload": "⬆️",
        "security": "🔒"
    }

# 日志配置
LOGGING_CONFIG = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'default': {
            'format': '[%(asctime)s] %(levelname)s in %(module)s: %(message)s',
        },
        'detailed': {
            'format': '[%(asctime)s] %(levelname)s [%(name)s.%(funcName)s:%(lineno)d] %(message)s',
        }
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'level': 'DEBUG',
            'formatter': 'default',
            'stream': 'ext://sys.stdout'
        },
        'file': {
            'class': 'logging.handlers.RotatingFileHandler',
            'level': 'INFO',
            'formatter': 'detailed',
            'filename': str(Config.LOG_FOLDER / 'app.log'),
            'maxBytes': Config.LOG_MAX_SIZE,
            'backupCount': Config.LOG_BACKUP_COUNT,
            'encoding': 'utf-8'
        },
        'error_file': {
            'class': 'logging.handlers.RotatingFileHandler',
            'level': 'ERROR',
            'formatter': 'detailed',
            'filename': str(Config.LOG_FOLDER / 'error.log'),
            'maxBytes': Config.LOG_MAX_SIZE,
            'backupCount': Config.LOG_BACKUP_COUNT,
            'encoding': 'utf-8'
        }
    },
    'loggers': {
        '': {
            'level': 'DEBUG',
            'handlers': ['console', 'file', 'error_file']
        }
    }
}
