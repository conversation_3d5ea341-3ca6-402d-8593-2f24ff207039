"""
工具函数模块
"""
import os
import hashlib
import re
from flask import request
from PIL import Image
import magic
from datetime import datetime

def get_client_ip():
    """获取客户端IP地址"""
    if request.headers.get('X-Forwarded-For'):
        return request.headers.get('X-Forwarded-For').split(',')[0].strip()
    elif request.headers.get('X-Real-IP'):
        return request.headers.get('X-Real-IP')
    else:
        return request.remote_addr

def validate_password(password):
    """验证密码强度"""
    from server.config.config import Config
    
    if len(password) < Config.PASSWORD_MIN_LENGTH:
        return False
    
    # 可以添加更多密码强度检查
    # 例如：必须包含大小写字母、数字、特殊字符等
    
    return True

def validate_email(email):
    """验证邮箱格式"""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def calculate_file_hash(file_path, algorithm='sha256'):
    """计算文件哈希值"""
    hash_func = hashlib.new(algorithm)
    
    try:
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_func.update(chunk)
        return hash_func.hexdigest()
    except Exception as e:
        print(f"计算文件哈希错误: {e}")
        return None

def get_file_type(file_path):
    """获取文件类型"""
    try:
        mime = magic.Magic(mime=True)
        return mime.from_file(file_path)
    except Exception:
        # 如果magic库不可用，使用文件扩展名
        _, ext = os.path.splitext(file_path)
        ext = ext.lower()
        
        # 常见文件类型映射
        type_mapping = {
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.png': 'image/png',
            '.gif': 'image/gif',
            '.bmp': 'image/bmp',
            '.tiff': 'image/tiff',
            '.pdf': 'application/pdf',
            '.doc': 'application/msword',
            '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            '.xls': 'application/vnd.ms-excel',
            '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            '.ppt': 'application/vnd.ms-powerpoint',
            '.pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            '.txt': 'text/plain',
            '.zip': 'application/zip',
            '.rar': 'application/x-rar-compressed',
            '.mp4': 'video/mp4',
            '.avi': 'video/x-msvideo',
            '.mp3': 'audio/mpeg',
            '.wav': 'audio/wav'
        }
        
        return type_mapping.get(ext, 'application/octet-stream')

def create_thumbnail(image_path, thumbnail_path, size=(200, 200), quality=80):
    """创建缩略图"""
    try:
        with Image.open(image_path) as img:
            # 转换为RGB模式（如果需要）
            if img.mode in ('RGBA', 'LA', 'P'):
                img = img.convert('RGB')
            
            # 创建缩略图
            img.thumbnail(size, Image.Resampling.LANCZOS)
            
            # 确保目录存在
            os.makedirs(os.path.dirname(thumbnail_path), exist_ok=True)
            
            # 保存缩略图
            img.save(thumbnail_path, 'JPEG', quality=quality, optimize=True)
            
            return True
    except Exception as e:
        print(f"创建缩略图错误: {e}")
        return False

def is_image_file(file_path):
    """检查是否为图像文件"""
    image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp'}
    _, ext = os.path.splitext(file_path.lower())
    return ext in image_extensions

def is_document_file(file_path):
    """检查是否为文档文件"""
    document_extensions = {'.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt'}
    _, ext = os.path.splitext(file_path.lower())
    return ext in document_extensions

def format_file_size(size_bytes):
    """格式化文件大小"""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"

def check_file_permission(user_id, shared_dir_id, permission_type):
    """检查用户对文件的权限"""
    from server.database.models import User, UserPermission
    
    try:
        user = User.query.get(user_id)
        if not user:
            return False
        
        # 管理员拥有所有权限
        if user.is_admin():
            return True
        
        # 获取用户对该目录的权限
        permissions = user.get_permissions_for_directory(shared_dir_id)
        
        # 检查具体权限
        permission_map = {
            'read': permissions.get('read', False),
            'write': permissions.get('write', False),
            'delete': permissions.get('delete', False),
            'download': permissions.get('download', False),
            'upload': permissions.get('upload', False)
        }
        
        return permission_map.get(permission_type, False)
        
    except Exception as e:
        print(f"检查权限错误: {e}")
        return False

def sanitize_filename(filename):
    """清理文件名，移除不安全字符"""
    # 移除路径分隔符和其他不安全字符
    unsafe_chars = ['/', '\\', ':', '*', '?', '"', '<', '>', '|']
    for char in unsafe_chars:
        filename = filename.replace(char, '_')
    
    # 移除前后空格和点
    filename = filename.strip(' .')
    
    # 确保文件名不为空
    if not filename:
        filename = 'unnamed_file'
    
    return filename

def generate_unique_filename(directory, filename):
    """生成唯一文件名"""
    base_name, ext = os.path.splitext(filename)
    counter = 1
    
    while os.path.exists(os.path.join(directory, filename)):
        filename = f"{base_name}_{counter}{ext}"
        counter += 1
    
    return filename

def extract_image_features(image_path):
    """提取图像特征（用于图像搜索）"""
    try:
        import cv2
        import numpy as np
        
        # 读取图像
        img = cv2.imread(image_path)
        if img is None:
            return None
        
        # 转换为灰度图
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # 计算直方图
        hist = cv2.calcHist([gray], [0], None, [256], [0, 256])
        hist = hist.flatten()
        
        # 归一化
        hist = hist / np.sum(hist)
        
        # 计算图像的基本统计信息
        mean_val = np.mean(gray)
        std_val = np.std(gray)
        
        # 使用ORB特征检测器
        orb = cv2.ORB_create(nfeatures=100)
        keypoints, descriptors = orb.detectAndCompute(gray, None)
        
        features = {
            'histogram': hist.tolist(),
            'mean': float(mean_val),
            'std': float(std_val),
            'keypoints_count': len(keypoints),
            'descriptors': descriptors.tolist() if descriptors is not None else []
        }
        
        return features
        
    except Exception as e:
        print(f"提取图像特征错误: {e}")
        return None

def compare_image_features(features1, features2, threshold=0.8):
    """比较两个图像的特征相似度"""
    try:
        if not features1 or not features2:
            return 0.0
        
        # 比较直方图
        hist1 = np.array(features1.get('histogram', []))
        hist2 = np.array(features2.get('histogram', []))
        
        if len(hist1) > 0 and len(hist2) > 0:
            # 使用巴氏距离
            hist_similarity = cv2.compareHist(hist1.astype(np.float32), hist2.astype(np.float32), cv2.HISTCMP_BHATTACHARYYA)
            hist_similarity = 1.0 - hist_similarity  # 转换为相似度
        else:
            hist_similarity = 0.0
        
        # 比较统计信息
        mean_diff = abs(features1.get('mean', 0) - features2.get('mean', 0)) / 255.0
        std_diff = abs(features1.get('std', 0) - features2.get('std', 0)) / 255.0
        stats_similarity = 1.0 - (mean_diff + std_diff) / 2.0
        
        # 综合相似度
        overall_similarity = (hist_similarity * 0.7 + stats_similarity * 0.3)
        
        return max(0.0, min(1.0, overall_similarity))
        
    except Exception as e:
        print(f"比较图像特征错误: {e}")
        return 0.0

def log_user_activity(user_id, activity_type, description, details=None):
    """记录用户活动"""
    from server.database.models import db, SystemLog
    
    try:
        log = SystemLog(
            log_level='INFO',
            log_type=activity_type,
            user_id=user_id,
            message=description,
            details=details,
            client_ip=get_client_ip()
        )
        db.session.add(log)
        db.session.commit()
    except Exception as e:
        print(f"记录用户活动错误: {e}")
        db.session.rollback()

def create_directory_if_not_exists(directory_path):
    """创建目录（如果不存在）"""
    try:
        os.makedirs(directory_path, exist_ok=True)
        return True
    except Exception as e:
        print(f"创建目录错误: {e}")
        return False
