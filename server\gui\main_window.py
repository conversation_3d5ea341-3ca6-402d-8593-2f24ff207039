"""
服务端主窗口界面
"""
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import customtkinter as ctk
from pathlib import Path
import threading
import sys
import os

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from server.config.config import GUIConfig, Config
from server.gui.components.sidebar import Sidebar
from server.gui.components.header import Header
from server.gui.components.footer import Footer
from server.gui.pages.dashboard import DashboardPage
from server.gui.pages.users import UsersPage
from server.gui.pages.files import FilesPage
from server.gui.pages.settings import SettingsPage
from server.gui.pages.logs import LogsPage
from server.gui.pages.monitor import MonitorPage

class MainWindow:
    """主窗口类"""
    
    def __init__(self):
        self.root = None
        self.current_page = None
        self.pages = {}
        self.server_thread = None
        self.server_running = False
        
        self.init_gui()
        self.create_widgets()
        self.setup_layout()
        
    def init_gui(self):
        """初始化GUI设置"""
        # 设置主题
        ctk.set_appearance_mode(GUIConfig.THEME_MODE)
        ctk.set_default_color_theme(GUIConfig.COLOR_THEME)
        
        # 创建主窗口
        self.root = ctk.CTk()
        self.root.title(GUIConfig.WINDOW_TITLE)
        self.root.geometry(f"{GUIConfig.WINDOW_MIN_WIDTH}x{GUIConfig.WINDOW_MIN_HEIGHT}")
        self.root.minsize(GUIConfig.WINDOW_MIN_WIDTH, GUIConfig.WINDOW_MIN_HEIGHT)
        
        # 设置窗口图标
        if GUIConfig.WINDOW_ICON.exists():
            self.root.iconbitmap(str(GUIConfig.WINDOW_ICON))
        
        # 设置字体
        self.default_font = ctk.CTkFont(
            family=GUIConfig.FONT_FAMILY,
            size=GUIConfig.FONT_SIZE
        )
        self.title_font = ctk.CTkFont(
            family=GUIConfig.FONT_FAMILY,
            size=GUIConfig.TITLE_FONT_SIZE,
            weight="bold"
        )
        
        # 绑定窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
    def create_widgets(self):
        """创建界面组件"""
        # 创建主框架
        self.main_frame = ctk.CTkFrame(self.root)
        self.main_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        # 创建头部
        self.header = Header(self.main_frame, self)
        
        # 创建内容区域框架
        self.content_frame = ctk.CTkFrame(self.main_frame)
        self.content_frame.pack(fill="both", expand=True, pady=(5, 0))
        
        # 创建侧边栏
        self.sidebar = Sidebar(self.content_frame, self)
        
        # 创建页面容器
        self.page_container = ctk.CTkFrame(self.content_frame)
        self.page_container.pack(side="right", fill="both", expand=True, padx=(5, 0))
        
        # 创建底部状态栏
        self.footer = Footer(self.main_frame, self)
        
        # 初始化页面
        self.init_pages()
        
    def setup_layout(self):
        """设置布局"""
        # 配置网格权重
        self.root.grid_rowconfigure(0, weight=1)
        self.root.grid_columnconfigure(0, weight=1)
        
    def init_pages(self):
        """初始化所有页面"""
        self.pages = {
            'dashboard': DashboardPage(self.page_container, self),
            'users': UsersPage(self.page_container, self),
            'files': FilesPage(self.page_container, self),
            'settings': SettingsPage(self.page_container, self),
            'logs': LogsPage(self.page_container, self),
            'monitor': MonitorPage(self.page_container, self)
        }
        
        # 默认显示仪表板
        self.show_page('dashboard')
        
    def show_page(self, page_name):
        """显示指定页面"""
        if self.current_page:
            self.current_page.hide()
            
        if page_name in self.pages:
            self.current_page = self.pages[page_name]
            self.current_page.show()
            self.sidebar.set_active_button(page_name)
            
    def start_server(self):
        """启动服务器"""
        if not self.server_running:
            self.server_thread = threading.Thread(target=self._run_server, daemon=True)
            self.server_thread.start()
            self.server_running = True
            self.header.update_server_status(True)
            self.footer.update_status("服务器已启动")
            
    def stop_server(self):
        """停止服务器"""
        if self.server_running:
            self.server_running = False
            self.header.update_server_status(False)
            self.footer.update_status("服务器已停止")
            
    def _run_server(self):
        """运行服务器（在后台线程中）"""
        try:
            from server.api.app import create_app
            app = create_app()
            app.run(
                host=Config.SERVER_HOST,
                port=Config.SERVER_PORT,
                debug=False,
                use_reloader=False
            )
        except Exception as e:
            self.footer.update_status(f"服务器启动失败: {str(e)}")
            self.server_running = False
            self.header.update_server_status(False)
            
    def restart_server(self):
        """重启服务器"""
        self.stop_server()
        # 等待一秒后重启
        self.root.after(1000, self.start_server)
        
    def show_about(self):
        """显示关于对话框"""
        about_text = f"""
{Config.APP_NAME}
版本: {Config.VERSION}

这是一个企业级文件共享系统，支持：
• 内外网访问控制
• 双搜索引擎（文件名 + 图像识别）
• 实时监控和日志记录
• 用户权限管理
• 文件加密下载
• 缩略图支持

技术栈: Python + Flask + MySQL + CustomTkinter
        """
        
        messagebox.showinfo("关于", about_text)
        
    def show_settings(self):
        """显示设置页面"""
        self.show_page('settings')
        
    def export_logs(self):
        """导出日志"""
        file_path = filedialog.asksaveasfilename(
            title="导出日志",
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )
        
        if file_path:
            try:
                # 这里实现日志导出逻辑
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write("系统日志导出\n")
                    f.write("=" * 50 + "\n")
                    # 添加实际的日志内容
                    
                messagebox.showinfo("成功", f"日志已导出到: {file_path}")
            except Exception as e:
                messagebox.showerror("错误", f"导出日志失败: {str(e)}")
                
    def backup_database(self):
        """备份数据库"""
        file_path = filedialog.asksaveasfilename(
            title="备份数据库",
            defaultextension=".sql",
            filetypes=[("SQL文件", "*.sql"), ("所有文件", "*.*")]
        )
        
        if file_path:
            try:
                # 这里实现数据库备份逻辑
                messagebox.showinfo("成功", f"数据库已备份到: {file_path}")
            except Exception as e:
                messagebox.showerror("错误", f"备份数据库失败: {str(e)}")
                
    def on_closing(self):
        """窗口关闭事件处理"""
        if messagebox.askokcancel("退出", "确定要退出服务端管理程序吗？"):
            if self.server_running:
                self.stop_server()
            self.root.destroy()
            
    def run(self):
        """运行主程序"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.on_closing()

def main():
    """主函数"""
    try:
        # 创建必要的目录
        Config.init_app(None)
        
        # 启动GUI
        app = MainWindow()
        app.run()
        
    except Exception as e:
        messagebox.showerror("启动错误", f"程序启动失败: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
